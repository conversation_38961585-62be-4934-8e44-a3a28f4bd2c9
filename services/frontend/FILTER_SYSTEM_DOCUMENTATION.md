# Filter System Documentation

## Overview

This document explains the new refactored filter system that replaces the old filter implementation. The new system provides better type safety, improved performance, and more consistent behavior across all table components.

## Key Improvements

### 🎯 **Main Problem Solved**
- **Date filters now work correctly**: Date values are properly formatted as `yyyy-MM-dd` instead of raw JavaScript date strings
- **Consistent filtering across all view modes**: Filters work in both table and cards view
- **Better type safety**: Proper type detection and conversion for all filter types

### 🚀 **New Features**
- **Boolean filter support**: Dropdown selection for true/false values
- **Enhanced type detection**: Automatic field type detection using FilterTypeManager
- **Universal filter system**: Works consistently across all tables (devices, employees, etc.)
- **Better debugging**: Comprehensive debug logging for troubleshooting

## Architecture Overview

### Core Components

#### 1. **FilterTypeManager** (`src/lib/filter-type-manager.ts`)
- **Purpose**: Central registry for table column types and formatting rules
- **Key Features**:
  - Registers table schemas with field types
  - Handles type conversion (parsing and formatting)
  - Supports date, number, boolean, and text types
  - Provides caching for performance

#### 2. **useTableRegistration Hook** (`src/hooks/useTableRegistration.ts`)
- **Purpose**: Registers table columns with FilterTypeManager
- **Usage**: `useTableRegistration("tableName", columns)`
- **Required**: Must be called in every component that uses filters

#### 3. **Enhanced Filter Components**
- **FilterModal**: Now separates translation prefixes from table prefixes
- **FilterInput**: Supports all data types with appropriate UI components
- **FilterRule**: Enhanced rule management with better type handling

### New Hook Architecture

#### **useFilterParams** (`src/hooks/filters/useFilterParams.ts`)
- Parses URL filter parameters
- Converts string values to proper types
- Handles complex filter structures

#### **useFilterNavigation** (`src/hooks/filters/useFilterNavigation.ts`)
- Manages filter URL updates
- Handles navigation with filter state
- Provides clean URL management

## Migration Guide

### Old vs New System

#### **Old System Issues**
```typescript
// ❌ Old: Raw date strings in URL
filter[created_at_eq]=Tue+Jun+03+2025+00%3A00%3A00+GMT+0300

// ❌ Old: Inconsistent type handling
// ❌ Old: Filters didn't work in cards view
// ❌ Old: No central type management
```

#### **New System Benefits**
```typescript
// ✅ New: Properly formatted dates
filter[created_at_eq]=2025-06-03

// ✅ New: Consistent type handling across all components
// ✅ New: Filters work in both table and cards view
// ✅ New: Central FilterTypeManager for all type operations
```

### Required Changes for New Tables

#### 1. **Add Table Registration**
```typescript
// In your table component
import { useTableRegistration } from "@/hooks/useTableRegistration";

const YourTableComponent = () => {
  // Register table with FilterTypeManager
  useTableRegistration("yourTableName", columns);
  
  // ... rest of component
};
```

#### 2. **Define Column Types**
```typescript
// In your column definitions
export const columns: ColumnDef<YourDataType>[] = [
  {
    accessorKey: "created_at",
    header: "Created Date",
    meta: {
      filterType: "date" // ← Important: Define filter type
    }
  },
  {
    accessorKey: "is_active",
    header: "Active",
    meta: {
      filterType: "boolean" // ← Boolean support
    }
  },
  // ... other columns
];
```

#### 3. **Update Both View Modes**
```typescript
// For tables with cards view, add registration to BOTH components:

// In table component
useTableRegistration("yourTable", columns);

// In cards component  
useTableRegistration("yourTable", columns); // Same registration!
```

## Supported Filter Types

### 1. **Date Filters**
- **Input**: Date picker component
- **Output**: `yyyy-MM-dd` format
- **Example**: `filter[created_at_eq]=2025-06-03`

### 2. **Boolean Filters**
- **Input**: Select dropdown (True/False)
- **Output**: `"true"` or `"false"` strings
- **Example**: `filter[is_active_eq]=true`

### 3. **Number Filters**
- **Input**: Number input
- **Output**: Numeric strings
- **Example**: `filter[salary_gt]=50000`

### 4. **Text Filters**
- **Input**: Text input
- **Output**: String values
- **Example**: `filter[name_cont]=john`

## Implementation Examples

### Complete Table Setup
```typescript
// 1. Define columns with filter types
export const columns: ColumnDef<Device>[] = [
  {
    accessorKey: "name",
    header: "Device Name",
    meta: { filterType: "text" }
  },
  {
    accessorKey: "created_at", 
    header: "Created Date",
    meta: { filterType: "date" }
  },
  {
    accessorKey: "is_active",
    header: "Active Status", 
    meta: { filterType: "boolean" }
  }
];

// 2. Register in table component
const DeviceTable = () => {
  useTableRegistration("devices", columns);
  // ... component logic
};

// 3. Register in cards component (if exists)
const DeviceCards = () => {
  useTableRegistration("devices", columns);
  // ... component logic  
};
```

## Debugging

The new system includes comprehensive debug logging:

```javascript
// FilterTypeManager logs
🔥 REGISTERING TABLE: devices with 11 columns
🔥 getFieldConfig: looking for devices.created_at
🔥 FilterTypeManager.formatValue ENTRY: {...}

// Filter processing logs  
🚨 formatFilterValue ENTRY: {...}
🔍 formatFilterValues called with: {...}
```

## Breaking Changes

### Removed Files
- `src/hooks/useFilterState.ts` → Replaced by new filter hooks
- `src/hooks/useFilterUrl.ts` → Replaced by useFilterNavigation
- `src/components/table/filter/filter-url-parser.ts` → Replaced by ransack-utils
- `src/components/table/filter/filter-utils-mapping.ts` → Replaced by FilterTypeManager

### Updated APIs
- **FilterModal**: Now requires `tablePrefix` prop
- **FilterInput**: Now supports `translationPrefix` prop
- **Table registration**: Must use `useTableRegistration` hook

## Best Practices

1. **Always register tables**: Every filterable table must call `useTableRegistration`
2. **Define filter types**: Add `meta.filterType` to all filterable columns
3. **Use simple table names**: Use `"devices"` not `"people.devices-page.table"`
4. **Register in all view modes**: Both table and cards components need registration
5. **Test filter functionality**: Verify filters work in both view modes

## Troubleshooting

### Common Issues

#### **Filters not working**
- Check if `useTableRegistration` is called
- Verify table name matches between registration and filter calls
- Ensure columns have `meta.filterType` defined

#### **Date filters showing raw strings**
- Verify table registration uses simple name (e.g., `"devices"`)
- Check FilterTypeManager debug logs for field config
- Ensure `created_at` column has `meta: { filterType: "date" }`

#### **Filters not working in cards view**
- Add `useTableRegistration` to cards component
- Ensure both table and cards use same table name

## Future Enhancements

- **Custom filter types**: Support for complex custom filters
- **Filter presets**: Save and load common filter combinations  
- **Advanced operators**: Support for more complex query operators
- **Filter validation**: Client-side validation for filter values
