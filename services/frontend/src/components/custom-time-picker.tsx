"use client";

import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import { Button } from "./ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { convertTo12Hour, to24HourFormat } from "@/lib/time-format";

type CustomTimePickerProps = {
  value: string;
  onChange: (value: string) => void;
  locale?: string;
  maxWidth?: string;
};

const CustomTimePicker = ({
  value,
  onChange,
  locale = "en",
  maxWidth = "120px",
}: CustomTimePickerProps) => {
  const t = useTranslations();

  // parseTime handles both 12-hour (with space) and 24-hour inputs.
  const parseTime = (
    val: string,
  ): { hour: string; minute: string; period: string } => {
    if (!val) {
      return {
        hour: "9",
        minute: "00",
        period: locale === "ar" ? "صباحا" : "AM",
      };
    }
    if (val.includes(" ")) {
      const [time, period] = val.split(" ");
      const [h, m] = time.split(":");
      return {
        hour: h,
        minute: m,
        period: period || (locale === "ar" ? "صباحا" : "AM"),
      };
    } else {
      return convertTo12Hour(val, locale);
    }
  };

  const initialTime = parseTime(value);
  const [hour, setHour] = useState<string>(initialTime.hour);
  const [minute, setMinute] = useState<string>(initialTime.minute);
  const [period, setPeriod] = useState<string>(initialTime.period);
  const [open, setOpen] = useState<boolean>(false);

  useEffect(() => {
    const parsed = parseTime(value);
    setHour(parsed.hour);
    setMinute(parsed.minute);
    setPeriod(parsed.period);
  }, [value, locale]);

  // When saving, convert to 24-hour format.
  const handleSave = () => {
    let standardizedPeriod = period;
    if (locale === "ar") {
      if (period === "صباحا") standardizedPeriod = "AM";
      else if (period === "مساءا") standardizedPeriod = "PM";
    }
    const backendTime = to24HourFormat(hour, minute, standardizedPeriod);
    onChange?.(backendTime);
    setOpen(false);
  };

  const displayTime = `${hour}:${minute} ${period}`;

  // Custom handler for popover open state changes.
  const handlePopoverOpenChange = (isOpen: boolean) => {
    if (!isOpen && open) {
      // If the popover is closing, save automatically.
      handleSave();
    }
    setOpen(isOpen);
  };

  return (
    <div style={{ maxWidth, width: "100%" }}>
      <Popover open={open} onOpenChange={handlePopoverOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="min-w-[120px] p-1 text-base max-sm:font-bold leading-6"
          >
            {displayTime}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-4">
          <div className="flex items-center gap-2 mb-4">
            {/* Hour Select */}
            <Select value={hour} onValueChange={setHour}>
              <SelectTrigger className="w-[80px] text-black">
                <SelectValue placeholder="Hour" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 12 }, (_, i) => i + 1).map((h) => {
                  const hourVal = String(h);
                  return (
                    <SelectItem key={h} value={hourVal}>
                      {hourVal}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            <span>:</span>
            {/* Minute Select */}
            <Select value={minute} onValueChange={setMinute}>
              <SelectTrigger className="w-[80px] text-black">
                <SelectValue placeholder="Minute" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 60 }, (_, i) => {
                  const mm = i < 10 ? `0${i}` : String(i);
                  return (
                    <SelectItem key={i} value={mm}>
                      {mm}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            {/* Period Select */}
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-[80px] text-black">
                <SelectValue placeholder="Period" />
              </SelectTrigger>
              <SelectContent>
                {locale === "ar" ? (
                  <>
                    <SelectItem value="صباحا">صباحا</SelectItem>
                    <SelectItem value="مساءا">مساءا</SelectItem>
                  </>
                ) : (
                  <>
                    <SelectItem value="AM">AM</SelectItem>
                    <SelectItem value="PM">PM</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={handleSave}>
              {t("common.buttonText.Save")}
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default CustomTimePicker;
