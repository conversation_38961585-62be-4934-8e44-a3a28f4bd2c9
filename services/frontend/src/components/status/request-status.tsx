import { REQUEST_STATUS } from "@/constants/enum";

const statusBackgroundColors = {
  [REQUEST_STATUS.ACCEPTED]: "bg-success-50",
  [REQUEST_STATUS.APPROVED]: "bg-success-50",
  [REQUEST_STATUS.REJECTED]: "bg-red-50",
  [REQUEST_STATUS.WAITING]: "bg-neutral-100",
  [REQUEST_STATUS.PENDING]: "bg-neutral-100",
  [REQUEST_STATUS.COMPLETED]: "bg-success-50",
  [REQUEST_STATUS.WITHDRAWN]: "bg-orange-50",
};

const statusTextColors = {
  [REQUEST_STATUS.ACCEPTED]: "text-green-900",
  [REQUEST_STATUS.APPROVED]: "text-green-900",
  [REQUEST_STATUS.REJECTED]: "text-red-500",
  [REQUEST_STATUS.WAITING]: "text-neutral-600",
  [REQUEST_STATUS.PENDING]: "text-neutral-600",
  [REQUEST_STATUS.COMPLETED]: "text-gray-900",
  [REQUEST_STATUS.WITHDRAWN]: "text-orange-700",
};

const RequestStatus = ({
  status,
  label,
}: {
  status: REQUEST_STATUS;
  label: string;
}) => {
  // Default to neutral styling if status is not recognized
  const bgColor = statusBackgroundColors[status] || "bg-neutral-100";
  const textColor = statusTextColors[status] || "text-neutral-600";

  return (
    <div
      className={`min-w-24 max-w-24 min-h-[27px] text-center leading-[27px] px-3 rounded-sm text-sm font-semibold tracking-tight ${bgColor} ${textColor}`}
    >
      {label}
    </div>
  );
};

export default RequestStatus;
