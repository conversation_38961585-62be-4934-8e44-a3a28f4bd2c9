"use client";

import React, { useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useFileUpload } from "./use-file-upload";
import FileItem from "./file-item";
import UploadButton from "./upload-button";

type FileAttachmentsInputProps = {
  field: {
    value: File[];
    onChange: (files: File[]) => void;
  };
  label?: string;
  isPending?: boolean;
  readOnly?: boolean;
  // Customization props
  accept?: string;
  multiple?: boolean;
  maxFiles?: number;
  maxSize?: number;
  containerClassName?: string;
  inputClassName?: string;
  fileListClassName?: string;
  buttonClassName?: string;
  // Translation keys
  uploadButtonText?: string;
  uploadTranslationKey?: string;
  // Optional callback when files change
  onFilesChange?: (files: File[]) => void;
  // Button icon
  uploadButtonIcon?: React.ReactNode;
  // Enable/disable drag and drop functionality
  enableDragDrop?: boolean;
};

export default function FileAttachmentsInput({
  field,
  isPending,
  readOnly,
  accept = "*/*",
  multiple = true,
  maxFiles = 10,
  maxSize = 5000000, // 5MB default
  containerClassName,
  fileListClassName,
  buttonClassName,
  uploadButtonText,
  uploadTranslationKey = "common.form.attachments.buttons.upload",
  onFilesChange,
  uploadButtonIcon,
  enableDragDrop = true,
}: FileAttachmentsInputProps) {
  const hiddenInputRef = useRef<HTMLInputElement | null>(null);
  const fileListRef = useRef<HTMLDivElement | null>(null);
  const dropZoneRef = useRef<HTMLDivElement | null>(null);

  const {
    files,
    isDragging,
    shouldScroll,
    setShouldScroll,
    handleFileChange,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleRemoveFile,
  } = useFileUpload({
    field,
    maxFiles,
    maxSize,
    accept,
    onFilesChange,
  });

  // Effect to scroll to the bottom of the file list when new files are added
  useEffect(() => {
    if (shouldScroll && fileListRef.current) {
      // Use setTimeout to ensure the DOM has fully updated
      setTimeout(() => {
        // Get the last file element
        const lastFile = fileListRef.current?.lastElementChild;
        if (lastFile) {
          lastFile.scrollIntoView({ behavior: "smooth", block: "start" });
        }
        setShouldScroll(false);
      }, 100); // Short delay to ensure DOM update
    }
  }, [shouldScroll, files, setShouldScroll]);

  return (
    <div
      ref={dropZoneRef}
      className={cn(
        "flex flex-col gap-4",
        enableDragDrop &&
          isDragging &&
          "border-2 border-dashed border-primary bg-primary/5 rounded-lg",
        containerClassName,
      )}
      onDragOver={enableDragDrop ? handleDragOver : undefined}
      onDragLeave={enableDragDrop ? handleDragLeave : undefined}
      onDrop={enableDragDrop ? handleDrop : undefined}
    >
      {/* Upload button */}
      <UploadButton
        onClick={() => hiddenInputRef.current?.click()}
        buttonClassName={buttonClassName}
        uploadButtonText={uploadButtonText}
        uploadTranslationKey={uploadTranslationKey}
        uploadButtonIcon={uploadButtonIcon}
        isPending={isPending}
        readOnly={readOnly}
        isDisabled={files.length >= maxFiles}
      />

      {/* Hidden file input */}
      <Input
        type="file"
        ref={hiddenInputRef}
        onChange={handleFileChange}
        accept={accept}
        multiple={multiple}
        className="hidden"
        disabled={isPending || readOnly}
      />

      {/* File list */}
      {files.length > 0 && (
        <div
          ref={fileListRef}
          className={cn("flex flex-col gap-2", fileListClassName)}
        >
          {files.map((file, index) => (
            <FileItem
              key={`${file.name}-${index}`}
              file={file}
              index={index}
              onRemove={handleRemoveFile}
              isPending={isPending}
              readOnly={readOnly}
            />
          ))}
        </div>
      )}
    </div>
  );
}
