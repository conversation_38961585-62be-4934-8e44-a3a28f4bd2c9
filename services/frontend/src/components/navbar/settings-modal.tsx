"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import { useLocale, useTranslations } from "next-intl";
import { ChevronLeftIcon, Settings, X } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { LANGUAGES } from "@/constants/enum";
import { Bell, CalendarRemove } from "../../../public/images/icons";
import ChangePasswordForm from "@/app/[locale]/_components/settings/change-password";
import EditProfileForm from "@/app/[locale]/_components/settings/edit-profile";
import NotificationSound from "@/app/[locale]/_components/settings/notification-sound";
import Holidays from "@/app/[locale]/_components/settings/holidays";
import { Locale } from "@/i18n/routing";
import { useA<PERSON>danceExemptions } from "@/app/[locale]/_modules/people/hooks/attendance/useAttendanceExemptions";

type SettingsModalProps = {
  open: boolean;
  activeTab: "GENERAL" | "NOTIFICATIONS" | "HOLIDAYS";
  subForm: "EDIT_PROFILE" | "CHANGE_PASSWORD" | null;
  openSettings: (
    tab: "GENERAL" | "NOTIFICATIONS" | "HOLIDAYS",
    subForm?: "EDIT_PROFILE" | "CHANGE_PASSWORD",
  ) => void;
  onClose: () => void;
};

export default function SettingsModal({
  open,
  activeTab,
  subForm,
  onClose,
  openSettings,
}: SettingsModalProps) {
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();
  const { isLoading } = useAttendanceExemptions();

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        lang={locale}
        hideClose={true}
        className="w-full max-w-[836px] min-h-[596.6px] flex flex-col !rounded-2xl p-0 pb-5 gap-0"
      >
        {/* Modal Header */}
        <DialogHeader className="relative flex flex-row text-start items-center border-b h-[75px]">
          <DialogTitle className="text-start flex items-center max-w-[217px] w-full border-e h-full ps-6">
            {t("settings.title")}
          </DialogTitle>

          <DialogDescription className="text-start px-10">
            {subForm === "CHANGE_PASSWORD" && activeTab === "GENERAL" ? (
              <span>{t("common.settings.tabs.change-password")}</span>
            ) : subForm === "EDIT_PROFILE" && activeTab === "GENERAL" ? (
              <span className="sr-only">
                {t("common.navbar.userProfile.editProfile")}
              </span>
            ) : activeTab === "HOLIDAYS" ? (
              <span className="sr-only">{t("settings.holidays.title")}</span>
            ) : (
              <span className="sr-only">
                {t("settings.notifications.title")}
              </span>
            )}
          </DialogDescription>

          <DialogClose asChild>
            <Button
              variant="ghost"
              className="absolute top-3 end-0 text-icons-main hover:text-secondary transition-colors"
            >
              <X className="!h-6 !w-6" />
            </Button>
          </DialogClose>
        </DialogHeader>

        {/* Tabs */}
        <Tabs
          orientation="vertical"
          lang={locale}
          dir={isAr ? "rtl" : "ltr"}
          value={activeTab}
          onValueChange={(newValue) =>
            openSettings(newValue as "GENERAL" | "NOTIFICATIONS" | "HOLIDAYS")
          }
          className="bg-none flex flex-1 border-s"
        >
          <TabsList className="flex flex-col bg-transparent w-full h-full border-e rounded-none max-w-[217px] justify-start p-0">
            <TabsTrigger
              className="w-full justify-start gap-3 active:bg-secondary-2 data-[state=active]:shadow-none data-[state=active]:bg-background-v2 data-[state=active]:border-e-2 data-[state=active]:border-secondary rounded-none h-11"
              value="GENERAL"
              onClick={() => openSettings("GENERAL", subForm || "EDIT_PROFILE")}
            >
              <Settings className="fill-none w-[18px] h-[18px] stroke-[1.5px] stroke-icons-main" />
              <span className="font-medium text-sm">
                {t("settings.global.title")}
              </span>
            </TabsTrigger>
            <TabsTrigger
              className="w-full justify-start gap-3 active:bg-secondary-2 data-[state=active]:shadow-none data-[state=active]:bg-background-v2 data-[state=active]:border-e-2 data-[state=active]:border-secondary rounded-none h-11"
              value="NOTIFICATIONS"
              onClick={() => openSettings("NOTIFICATIONS")}
            >
              <Bell className="fill-none w-[18px] h-[18px] stroke-[1.5px] stroke-icons-main" />
              <span className="font-medium text-sm">
                {t("settings.notifications.title")}
              </span>
            </TabsTrigger>
            <TabsTrigger
              disabled={isLoading}
              className="w-full justify-start gap-3 group active:bg-secondary-2 data-[state=active]:shadow-none data-[state=active]:bg-background-v2 data-[state=active]:border-e-2 data-[state=active]:border-secondary rounded-none h-11"
              value="HOLIDAYS"
              onClick={() => openSettings("HOLIDAYS")}
            >
              <CalendarRemove className="w-[18px] h-[18px] stroke-[1.5px] text-icons-main group-data-[state=active]:text-black" />
              <span className="font-medium text-sm">
                {t("settings.holidays.title")}
              </span>
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="GENERAL"
            className="pt-4 pb-5 px-10 flex-1 relative border-s border-border"
          >
            {subForm === "EDIT_PROFILE" && (
              <EditProfileForm
                onPasswordChanged={() =>
                  openSettings("GENERAL", "CHANGE_PASSWORD")
                }
              />
            )}
            {subForm === "CHANGE_PASSWORD" && (
              <div className="relative h-full">
                <Button
                  title="go to profile form"
                  aria-label="arrow back to edit form"
                  variant={"ghost"}
                  className="absolute -top-4 -end-5 bg-background-v2"
                  onClick={() => openSettings("GENERAL", "EDIT_PROFILE")}
                >
                  <ChevronLeftIcon className="!h-6 !w-6 ltr:rotate-180" />
                </Button>
                <ChangePasswordForm
                  onPasswordChanged={() =>
                    openSettings("GENERAL", "EDIT_PROFILE")
                  }
                />
              </div>
            )}
            {!subForm && (
              <EditProfileForm
                onPasswordChanged={() =>
                  openSettings("GENERAL", "CHANGE_PASSWORD")
                }
              />
            )}
          </TabsContent>

          <TabsContent value="NOTIFICATIONS" className="mt-6 px-10 w-full">
            <NotificationSound />
          </TabsContent>

          <TabsContent
            value="HOLIDAYS"
            className="mt-6 px-10 w-full max-h-[80vh] custom-scroll"
          >
            <Holidays />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
