"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ResponsivePopover } from "../../responsive-popover";
import { X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useMemo } from "react";
import { FilterModalProps, TFilterGroup } from "../types";
import { FilterRuleList } from "./filter-rule-list";
import { useTableFilter } from "@/hooks/use-table-filter";
import { SliderSettings } from "../../../../public/images/icons";

import { useFilterParams, useFilterNavigation } from "@/hooks/filters";
import { parseRansackKey, createRansackKey, isArrayOperator, parseFilterValues, formatFilterValues } from "./ransack-utils";

export function FilterModal<TData>({
  open,
  onOpenChange,
  children,
  table,
  translationPrefix,
  tableId,
}: FilterModalProps<TData>) {
  const t = useTranslations();
  const { filters } = useFilterParams();
  const { setFilters: setFiltersInUrl, clearFilters: clearFiltersFromUrl } = useFilterNavigation();

  const {
    groups,
    setGroups,
    ruleRefs,
    lastAddedRuleId,
    setLastAddedRuleId,
    addRule,
    removeRule,
    handleFilterChange,
    clearFilters,
  } = useTableFilter();

  // Clean up refs when rules change
  useEffect(() => {
    Object.keys(ruleRefs.current).forEach((ruleId) => {
      if (
        !groups.some((group) => group.rules.some((rule) => rule.id === ruleId))
      ) {
        delete ruleRefs.current[ruleId];
      }
    });

    if (lastAddedRuleId && ruleRefs.current[lastAddedRuleId]) {
      ruleRefs.current[lastAddedRuleId]?.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
      });
      setLastAddedRuleId(null);
    }
  }, [lastAddedRuleId, groups, ruleRefs, setLastAddedRuleId]);

  // Convert filters from new hook format to filter groups format
  const convertFiltersToGroups = (filters: Record<string, any>): TFilterGroup[] => {
    if (Object.keys(filters).length === 0) {
      return [{ id: `group-${Date.now()}`, rules: [{ id: `rule-${Date.now()}-0`, field: "", operator: "eq", value: "" }] }];
    }

    // Use universal helper - handles ALL type conversion
    const parsedFilters = tableId
      ? parseFilterValues(filters, tableId)
      : filters;

    const timestamp = Date.now();
    const rules = Object.entries(parsedFilters).flatMap(([key, value], index) => {
      const parsedKey = parseRansackKey(key);
      const field = parsedKey?.field || key;
      const operator = parsedKey?.operator || 'eq';

      if (isArrayOperator(operator)) return [];

      const values = Array.isArray(value) ? value : [value];

      return values.map((val, valIndex) => ({
        id: `rule-${timestamp}-${index}-${valIndex}`,
        field,
        operator,
        value: val // Already parsed by universal helper!
      }));
    });

    return [{ id: `group-${timestamp}`, rules }];
  };

  // Convert filters to groups using useMemo to prevent infinite loops
  const groupsFromFilters = useMemo(() => {
    const filterKeys = Object.keys(filters);
    if (filterKeys.length > 0) {
      return convertFiltersToGroups(filters);
    }
    return null;
  }, [JSON.stringify(filters)]); // Use JSON.stringify to ensure stable comparison

  // Update groups when filters change, but only if we have filters
  useEffect(() => {
    if (groupsFromFilters) {
      setGroups(groupsFromFilters);
    }
  }, [groupsFromFilters]);

  // Check if all groups have valid rules (with field, operator, and value when required)
  const hasValidFilters = () => {
    // Check if there are any groups
    if (groups.length === 0) return false;

    // Check if at least one group has valid rules
    return groups.some((group) => {
      // Check if the group has any rules
      if (group.rules.length === 0) return false;

      // Check if at least one rule in the group is valid
      return group.rules.some((rule) => {
        if (!rule.field || !rule.operator) return false;

        // Some operators don't require a value (null checks, boolean checks)
        const operatorsWithoutValue = ['null', 'not_null', 'present', 'blank', 'true', 'false'];
        const requiresValue = !operatorsWithoutValue.includes(rule.operator);

        // Rule is valid if it doesn't require a value, or if it has a value
        return !requiresValue || rule.value;
      });
    });
  };

  const applyFilters = () => {
    if (hasValidFilters()) {
      // Collect raw filter values - no type conversion here
      const rawFilters: Record<string, any> = {};

      groups[0]?.rules
        .filter(rule => rule.field && rule.operator && rule.value)
        .forEach(rule => {
          const key = createRansackKey(rule.field, rule.operator);

          if (isArrayOperator(rule.operator)) return;

          // Collect multiple values for same key - no formatting yet
          if (!rawFilters[key]) {
            rawFilters[key] = [];
          }
          rawFilters[key].push(rule.value);
        });

      // Convert arrays to single values where appropriate
      const flatFilters: Record<string, any> = {};
      Object.entries(rawFilters).forEach(([key, values]) => {
        flatFilters[key] = values.length === 1 ? values[0] : values;
      });

      // Use universal helper - handles ALL type conversion
      const formattedFilters = tableId
        ? formatFilterValues(flatFilters, tableId)
        : flatFilters;



      setFiltersInUrl(formattedFilters);
    } else {
      clearFiltersFromUrl();
    }
    onOpenChange(false);
  };

  // Handle key press events for the modal
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      applyFilters();
    }
  };

  // Create header content
  const headerContent = (
    <div className="flex items-center justify-between rounded-xl">
      <h2 className="text-lg sm:text-lg font-semibold leading-7 flex items-center gap-2">
        <span>
          <SliderSettings className="mr-2 scale-125" />
        </span>
        <span>{t("common.Table.filter")}</span>
      </h2>
      <Button
        variant="outline"
        className="border-gray-300 w-3 h-7 px-3.5 text-gray-700 rounded-lg opacity-70 transition-opacity hover:opacity-100 disabled:pointer-events-none"
        onClick={() => onOpenChange(false)}
      >
        <X className="!h-6 !w-6 stroke-[1.3px]" />
        <span className="sr-only">Close</span>
      </Button>
    </div>
  );

  // Handle clear filters - clear and automatically apply
  const handleClearFilters = () => {
    clearFilters();
    clearFiltersFromUrl();
    onOpenChange(false);
  };

  // Create footer content
  const footerContent = (
    <div className="rounded-[20px] flex flex-row items-center justify-between gap-4 sm:gap-6">
      <Button
        type="button"
        onClick={() => applyFilters()}
        className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg text-sm md:text-base"
        disabled={!hasValidFilters()}
      >
        {t("common.Table.apply").split(" ")[0]}
      </Button>
      <Button
        variant="outline"
        type="button"
        className="w-full h-12 sm:max-w-[244px] rounded-lg text-sm md:text-base"
        onClick={handleClearFilters}
      >
        {t("common.Table.clear")}
      </Button>
    </div>
  );

  return (
    <ResponsivePopover
      trigger={children}
      open={open}
      onOpenChange={onOpenChange}
      align="end"
      sideOffset={5}
      className="shadow-lg w-full max-w-[561px] md:w-[561px] rounded-[20px]"
      header={headerContent}
      footer={footerContent}
      headerClassName="px-6 py-5 border-b bg-white sticky top-0 z-10 rounded-[20px]"
      contentClassName="max-h-[40vh]"
    >
      <div
        className="p-6 space-y-6 max-w-[561px]"
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        {groups.length > 0 ? (
          groups.map((group) => (
            <div key={group.id} className="space-y-6">
              <FilterRuleList
                key={group.id}
                group={group}
                table={table}
                onUpdateRule={handleFilterChange}
                onAddRule={() => addRule(group.id)}
                onRemoveRule={(ruleId) => removeRule(group.id, ruleId)}
                translationPrefix={translationPrefix}
                t={t}
                ruleRefs={ruleRefs}
              />
            </div>
          ))
        ) : (
          <div className="min-h-[100px] border border-dashed border-gray-200 rounded-md flex items-center justify-center mb-6">
            <p className="text-gray-400 text-sm">
              {t("common.Table.filter-options.title")}
            </p>
          </div>
        )}
      </div>
    </ResponsivePopover>
  );
}
