"use client";

import React, { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import ResponsiveDialog from "@/components/responsive-dialog";
import { Indicator, type IndicatorStep } from "@/components/ui/step-indicator";
import { useTranslations } from "next-intl";

import type { ExportFormat, ExportFilters } from "@/types/export";
import { isDevelopment } from "@/utils/env";
export interface ExportColumn {
  id: string;
  label: string;
  description?: string;
  required?: boolean;
  category?: string;
}

// Utility functions moved here since they're only used in this component
export const getRequiredColumns = (columns: ExportColumn[]): string[] => {
  return columns.filter((col) => col.required).map((col) => col.id);
};

export const toggleColumnSelection = (
  columnId: string,
  selectedColumns: string[],
): string[] => {
  if (selectedColumns.includes(columnId)) {
    return selectedColumns.filter((id) => id !== columnId);
  } else {
    return [...selectedColumns, columnId];
  }
};
import { CSVEXE, PDFEXE, XSLEXE } from "../../../public/images/icons";

interface EnhancedExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (
    format: ExportFormat,
    selectedColumns: string[],
    filters?: ExportFilters,
  ) => Promise<void>;
  columns: ExportColumn[];
  supportedFormats: ExportFormat[];
  entityName: string;
  loading?: boolean;
  filters?: ExportFilters;
  defaultSelectedColumns?: string[];
}

export function EnhancedExportModal({
  isOpen,
  onClose,
  onExport,
  columns,
  supportedFormats,
  entityName,
  loading = false,
  filters,
  defaultSelectedColumns,
}: EnhancedExportModalProps) {
  const t = useTranslations();
  const [step, setStep] = useState<"columns" | "format">("columns");
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat | null>(
    null,
  );
  const [selectedColumns, setSelectedColumns] = useState<string[]>(
    defaultSelectedColumns ||
      columns.filter((col) => !col.required).map((col) => col.id),
  );

  const requiredColumns = getRequiredColumns(columns);
  const allSelectedColumns = [...requiredColumns, ...selectedColumns];

  const handleFormatSelect = (format: ExportFormat) => {
    setSelectedFormat(format);
  };

  const handleContinueToFormat = () => {
    setStep("format");
  };

  const handleColumnToggle = (columnId: string) => {
    setSelectedColumns((prev) => toggleColumnSelection(columnId, prev));
  };

  const handleExport = async () => {
    if (!selectedFormat) return;

    try {
      await onExport(selectedFormat, allSelectedColumns, filters);
      onClose();
      setStep("columns");
      setSelectedFormat(null);
    } catch (error) {
      // Error is already handled by the export hook with toast notifications
      if (isDevelopment()) {
        console.error("Export failed:", error);
      }
    }
  };

  const handleBack = () => {
    setStep("columns");
  };

  const handleClose = () => {
    onClose();
    setStep("columns");
    setSelectedFormat(null);
  };

  const steps: IndicatorStep[] = [
    {
      id: "columns",
      label: t("people.export.steps.selectFields") || "تحديد الحقول",
    },
    {
      id: "format",
      label: t("people.export.steps.selectFormat") || "تحديد الصيغة",
    },
  ];

  const headerContent = (
    <>
      <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px] text-start">
        {t("people.export.title")}
      </DialogTitle>
      <DialogDescription className="sr-only">
        Export {entityName} data in your preferred format and columns
      </DialogDescription>
    </>
  );

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={handleClose}
      header={headerContent}
      className="max-w-xl h-[93vh]"
    >
      <Indicator steps={steps} currentStep={step} className="py-6" />

      <div className="flex flex-col flex-1">
        {step === "columns" && (
          <div className="pt-4 flex flex-col min-h-[54vh]">
            <div className="flex-1">
              <h2 className="text-right text-base font-medium leading-5 mb-4">
                {t("people.export.steps.selectFields")}
              </h2>

              <div className="space-y-3 mb-8">
                {columns.map((column) => {
                  const isSelected = allSelectedColumns.includes(column.id);
                  const isRequired = column.required;

                  return (
                    <div
                      key={column.id}
                      className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
                        isSelected
                          ? "border-slate-800 bg-slate-50"
                          : "border-slate-200 bg-white hover:border-slate-300"
                      }`}
                      onClick={() =>
                        !isRequired && handleColumnToggle(column.id)
                      }
                    >
                      <div className="text-right text-sm font-medium text-slate-700">
                        {column.label}
                      </div>
                      {isRequired && (
                        <div className="absolute top-2 left-2">
                          <div className="w-2 h-2 bg-slate-400 rounded-full"></div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {step === "format" && (
          <div className="pt-4 flex flex-col min-h-[54vh]">
            {/* Title */}
            <h2 className="text-right text-base leading-5 font-medium mb-4">
              {t("people.export.chooseFormat")}
            </h2>

            {/* Format Icons Grid */}
            <div className="grid grid-cols-3 gap-4 mb-8">
              {supportedFormats.map((format) => {
                const isSelected = selectedFormat === format;

                // Define colors for each format to match the actual icon colors
                const formatColors = {
                  csv: "border-blue-600", // #2563EB
                  pdf: "border-red-500", // #FF3E4C
                  xlsx: "border-green-500", // #00C650
                  svg: "border-orange-500",
                  png: "border-blue-800",
                  doc: "border-blue-700",
                };

                const selectedColor =
                  formatColors[format as keyof typeof formatColors] ||
                  "border-blue-400";

                return (
                  <button
                    key={format}
                    onClick={() => handleFormatSelect(format)}
                    className={`relative min-w-[161px] min-h-[120px] p-6 rounded-2xl border-2 transition-all duration-200 bg-gray-50 hover:bg-gray-100 ${
                      isSelected
                        ? `${selectedColor} bg-white`
                        : "border-gray-200"
                    }`}
                  >
                    <div className="flex flex-col items-center justify-center ">
                      {/* File Extension Icons */}
                      <div className="flex items-center justify-center">
                        {format === "csv" && (
                          <CSVEXE className="w-full h-full" />
                        )}
                        {format === "pdf" && (
                          <PDFEXE className="w-full h-full" />
                        )}
                        {format === "xlsx" && (
                          <XSLEXE className="w-full h-full" />
                        )}
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Persistent Button Group */}
        <div className="sticky border-t border-t-slate-200 pt-2.5 bottom-0 left-0 w-[100%] rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6">
          {step === "columns" ? (
            <div className="pt-4 w-full flex gap-4">
              <Button
                onClick={handleContinueToFormat}
                disabled={allSelectedColumns.length === 0}
                className="flex-1 h-12 w-1/2"
              >
                {t("common.buttonText.next")}
              </Button>
              <Button
                variant="outline"
                onClick={handleClose}
                className="flex-1 h-12 w-1/2"
              >
                {t("common.buttonText.cancel")}
              </Button>
            </div>
          ) : (
            <div className="pt-4 w-full flex gap-4">
              <Button
                onClick={handleExport}
                disabled={loading || !selectedFormat}
                className="flex-1 h-12 w-1/2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    {t("people.export.exporting")}
                  </>
                ) : (
                  t("people.export.export")
                )}
              </Button>
              <Button
                variant="outline"
                onClick={handleBack}
                className="flex-1 h-12 w-1/2 "
              >
                {t("common.buttonText.back")}
              </Button>
            </div>
          )}
        </div>
      </div>
    </ResponsiveDialog>
  );
}
