"use client";

import React, { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { EnhancedExportModal } from "./enhanced-export-modal";
import {
  useEnhancedExport,
  generateExportColumns,
} from "@/hooks/useEnhancedExport";
import type { ExportColumn } from "./enhanced-export-modal";
import { getSupportedFormats } from "@/config/export-entities";
import type { ExportFormat, ExportFilters } from "@/types/export";
import { DocumentDownload } from "../../../public/images/icons";
import { Loader } from "lucide-react";

interface EnhancedExportControlsProps {
  entity: string;
  filters?: ExportFilters;
  disabled?: boolean;
  className?: string;
  // Column configuration
  tableColumns?: any[]; // TanStack table columns
  exportColumns?: ExportColumn[]; // Custom export columns
  columnCategories?: Record<string, string>;
  columnDescriptions?: Record<string, string>;
  requiredColumns?: string[];
  excludeColumns?: string[];
  defaultSelectedColumns?: string[];
}

export function EnhancedExportControls({
  entity,
  filters,
  disabled = false,
  className = "",
  tableColumns = [],
  exportColumns,
  columnCategories = {},
  columnDescriptions = {},
  requiredColumns = [],
  excludeColumns = ["select", "actions"],
  defaultSelectedColumns,
}: EnhancedExportControlsProps) {
  const t = useTranslations();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { exportData, loading, entityDisplayName } = useEnhancedExport(entity);
  const supportedFormats = getSupportedFormats(entity);

  // Generate columns from table columns if not provided
  const columns = useMemo(() => {
    if (exportColumns) {
      return exportColumns;
    }

    if (tableColumns.length > 0) {
      return generateExportColumns(tableColumns, {
        excludeColumns,
        requiredColumns,
        columnCategories,
        columnDescriptions,
      });
    }

    // Default columns if nothing is provided
    return [
      { id: "id", label: "ID", required: true, category: "Basic" },
      { id: "name", label: "Name", category: "Basic" },
      { id: "created_at", label: "Created Date", category: "Metadata" },
      { id: "updated_at", label: "Updated Date", category: "Metadata" },
    ];
  }, [
    exportColumns,
    tableColumns,
    excludeColumns,
    requiredColumns,
    columnCategories,
    columnDescriptions,
  ]);

  const handleExport = async (
    format: ExportFormat,
    selectedColumns: string[],
    exportFilters?: ExportFilters,
  ) => {
    await exportData(format, selectedColumns, exportFilters || filters);
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  if (supportedFormats.length === 0) {
    return null;
  }

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={handleOpenModal}
        disabled={disabled || loading}
        className={`flex items-center min-h-11 max-h-[48px] gap-2 rounded-lg ${className}`}
      >
        {loading ? (
          <Loader className="animate-spin !w-6" />
        ) : (
          <DocumentDownload className="!h-6 !w-6" />
        )}
      </Button>

      <EnhancedExportModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onExport={handleExport}
        columns={columns}
        supportedFormats={supportedFormats}
        entityName={entityDisplayName}
        loading={loading}
        filters={filters}
        defaultSelectedColumns={defaultSelectedColumns}
      />
    </>
  );
}
