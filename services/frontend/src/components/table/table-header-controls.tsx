"use client";

import { Table as TableType } from "@tanstack/react-table";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useLocale, useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { SearchNormal, SliderSettings } from "../../../public/images/icons";
import { LANGUAGES } from "@/constants/enum";
import { Locale, usePathname, useRouter } from "@/i18n/routing";
import { useSearchParams } from "next/navigation";
import { useState, useEffect, useCallback } from "react";
import { debounce } from "lodash";
import { FilterModal } from "./filter/filter-modal";

interface TableHeaderControlsProps<TData> {
  table: TableType<TData>;
  title?: string;
  translationPrefix: string;
  tablePrefix?: string; // Optional: for FilterTypeManager, will be derived if not provided
  isLoading: boolean;
  hideSearch?: boolean;
  hideFilters?: boolean;
  hideColumns?: boolean;
  // Add new prop for custom actions
  headerActions?: React.ReactNode;
}

export function TableHeaderControls<TData>({
  table,
  title,
  isLoading,
  translationPrefix,
  tablePrefix,
  hideSearch = false,
  hideFilters = false,
  hideColumns = false,
  headerActions,
}: TableHeaderControlsProps<TData>) {
  const t = useTranslations() as TFunction;
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [filterModalOpen, setFilterModalOpen] = useState(false);

  // Derive table prefix for FilterTypeManager if not provided
  // Convert "people.devices-page.table" -> "devices"
  const derivedTablePrefix = tablePrefix || (() => {
    const parts = translationPrefix.split('.');
    if (parts.length >= 2 && parts[1].endsWith('-page')) {
      return parts[1].replace('-page', '');
    }
    return translationPrefix; // fallback
  })();

  // Get the current search query from URL
  const [searchValue, setSearchValue] = useState(
    searchParams.get("search") || "",
  );

  // Create a debounced function to update URL parameters
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedUpdateUrl = useCallback(
    debounce((value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      if (value) {
        params.set("search", value);
      } else {
        params.delete("search");
      }
      router.replace(`${pathname}?${params.toString()}`);
    }, 500),
    [router, pathname, searchParams],
  );

  // Update URL when search value changes
  useEffect(() => {
    debouncedUpdateUrl(searchValue);

    // Clean up the debounced function on unmount
    return () => {
      debouncedUpdateUrl.cancel();
    };
  }, [searchValue, debouncedUpdateUrl]);

  return (
    <div className="flex items-start md:items-center flex-col-reverse md:flex-row-reverse justify-between">
      <div className="flex flex-1 flex-col w-full md:flex-row justify-end items-start md:items-center pb-4 pt-4 sm:pt-[18px] gap-4 sm:gap-5">
        {!hideSearch && (
          <div className="relative w-full md:max-w-[320px] h-10 md:w-[320px] flex items-center">
            <SearchNormal className="text-[#D1D5DB] absolute top-1/2 -translate-y-1/2 start-3 mt-0.5" />
            <Input
              placeholder={t("common.Table.searchPlaceholder")}
              value={searchValue}
              onChange={(event) => setSearchValue(event.target.value)}
              disabled={isLoading}
              className="md:max-w-[320px] ps-10 max-h-10 border shadow-none rounded-xl border-[#F3F4F6] outline-none focus-visible:ring-1 focus-visible:ring-secondary text-black placeholder:text-[#9CA3AF] text-sm font-normal disabled:opacity-70 disabled:cursor-not-allowed"
              aria-label={t("common.Table.searchPlaceholder")}
            />
          </div>
        )}
        <div className="flex max-md:flex-row max-lg:flex-col gap-2">
          {/* Column visibility dropdown */}
          {!hideColumns && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  disabled={isLoading}
                  className="border-[#DCE4E8] h-11 text-sm rounded-[9px] font-semibold disabled:opacity-70 disabled:cursor-not-allowed"
                  aria-label={t("common.Table.visibility")}
                >
                  <SliderSettings className="mr-2" />
                  {t("common.Table.visibility")}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align={isAr ? "start" : "end"}>
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize !w-full !max-w-full !min-w-full"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value: boolean) => {
                        if (!isLoading) {
                          column.toggleVisibility(!!value);
                        }
                      }}
                      disabled={isLoading}
                    >
                      {t(
                        String(
                          `${translationPrefix}.columns.${String(column.id)}`,
                        ),
                      )}
                    </DropdownMenuCheckboxItem>
                  ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Filter button and modal */}
          {!hideFilters && (
            <FilterModal
              translationPrefix={translationPrefix}
              tablePrefix={derivedTablePrefix}
              open={filterModalOpen}
              onOpenChange={setFilterModalOpen}
              table={table}
            >
              <Button
                variant="outline"
                disabled={isLoading}
                onClick={() => setFilterModalOpen(true)}
                className="border-[#DCE4E8] h-11 text-sm rounded-[9px] font-semibold disabled:opacity-70 disabled:cursor-not-allowed"
                aria-label={t("common.Table.filter")}
              >
                <SliderSettings className="mr-2" />
                {t("common.Table.filter")}
              </Button>
            </FilterModal>
          )}
        </div>

        {/* Add custom header actions */}
        {headerActions && <div>{headerActions}</div>}
      </div>

      {title && (
        <h3 className="text-sm text-[#6C7278] min-w-28 font-semibold">
          {title}
        </h3>
      )}
    </div>
  );
}
