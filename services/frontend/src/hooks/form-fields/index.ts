import { PAGES } from "@/enums";
import { TFunction, TinputField } from "@/types";
import { FieldValues } from "react-hook-form";

// Import all form getters
import {
  getLoginForm,
  getForgotPasswordForm,
  getResetPasswordForm,
  getVerifyCodeForm,
} from "./auth-forms";
import { getChangePasswordForm, getEditProfileForm } from "./profile-forms";
import {
  getUpdateTotalSalaryForm,
  getAddEmployeeForm,
  getAddDeviceForm,
  getDeviceCommandForm,
  getCreateLeaveForm,
  getCreateSalaryPackageForm,
  getHolidayForm,
} from "./people-forms";

export const getFormFieldsByType = <T extends FieldValues>(
  formType: string,
  t: TFunction,
): TinputField<T>[] => {
  switch (formType) {
    // Auth forms
    case PAGES.LOGIN:
      return getLoginForm(t) as unknown as <PERSON><PERSON><PERSON>ield<T>[];
    case PAGES.RESETPASS:
      return getResetPasswordForm(t) as unknown as TinputField<T>[];
    case PAGES.FORGOTPASS:
      return getForgotPasswordForm(t) as unknown as TinputField<T>[];
    case PAGES.VERIFYCODE:
      return getVerifyCodeForm(t) as unknown as TinputField<T>[];

    // Profile forms
    case PAGES.CHANGEPASS:
      return getChangePasswordForm(t) as unknown as TinputField<T>[];
    case PAGES.EDITPROFILE:
      return getEditProfileForm(t) as unknown as TinputField<T>[];

    // People module forms
    case PAGES.UPDATETOTALSALARY:
      return getUpdateTotalSalaryForm(t) as unknown as TinputField<T>[];
    case PAGES.ADDEMPLOYEE:
      return getAddEmployeeForm(t) as unknown as TinputField<T>[];
    case PAGES.ADDDEVICE:
      return getAddDeviceForm(t) as unknown as TinputField<T>[];
    case PAGES.DEVICECOMMAND:
      return getDeviceCommandForm(t) as unknown as TinputField<T>[];
    case PAGES.CREATELEAVE:
      return getCreateLeaveForm(t) as unknown as TinputField<T>[];
    case PAGES.CREATESALARYPACKAGE:
      return getCreateSalaryPackageForm(t) as unknown as TinputField<T>[];

    // Settings module forms
    case PAGES.ADDHOLIDAY:
      return getHolidayForm(t) as unknown as TinputField<T>[];

    default:
      console.warn(`Form fields for form type "${formType}" not found`);
      return [];
  }
};
