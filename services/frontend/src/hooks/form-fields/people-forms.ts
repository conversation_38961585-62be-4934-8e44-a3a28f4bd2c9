import { TinputField, TFunction } from "@/types";
import { EmployeeSchemaType } from "@/app/[locale]/_modules/people/schemas/employeeSchema";
import { SalarySchemaType } from "@/app/[locale]/_modules/people/schemas/salarySchema";
import { SalaryPackageSchemaType } from "@/app/[locale]/_modules/people/schemas/salaryPackageSchema";
import { CreateLeaveSchemaType } from "@/app/[locale]/_modules/people/schemas/leaveSchema";
import { DeviceSchemaType } from "@/app/[locale]/_modules/people/schemas/deviceSchema";
import { DeviceCommandSchemaType } from "@/app/[locale]/_modules/people/schemas/deviceCommandSchema";
import { DEPARTMENT_OPTIONS } from "@/app/[locale]/_modules/people/constants/employee";
import {
  LEAVE_DURATION,
  LEAVE_TYPE,
} from "@/app/[locale]/_modules/people/enum";
import { capitalize } from "lodash";
import { HolidaySchemaType } from "@/app/[locale]/_modules/settings/schemas/holiday-schema";

const getDepartmentTranslation = (department: string, t: TFunction) => {
  try {
    // Try to get the translation
    return t(
      `people.employees-page.add-employee-dialog.form.departments.${department}`,
    );
  } catch (error) {
    // If translation is missing, return capitalized department name
    return capitalize(department);
  }
};

export const getUpdateTotalSalaryForm = (
  t: TFunction,
): TinputField<SalarySchemaType>[] => [
  // Helper function to get department translations with fallbacks
  {
    name: "totalSalary",
    type: "text",
    className: " placeholder:text-disabled-text",
    labelClassName: "block",
    label: t(
      "people.employees-salaries-page.edit-salary-dialog.content.input-label",
    ),
    placeholder: "23,960",
    formatOnChange: true,
  },
  {
    name: "note",
    type: "textarea",
    className:
      "w-full rounded-[10px] min-h-[138px] max-h-[138px] placeholder:text-gray-400",
    label: t("people.leaves-requests-page.dialog-common.note-label"),
    labelClassName: "block  pt-5",
    placeholder: t(
      "people.employees-salaries-page.edit-salary-dialog.content.note-placeholder",
    ),
  },
];

export const getAddEmployeeForm = (
  t: TFunction,
): TinputField<EmployeeSchemaType>[] => [
  {
    name: "avatar",
    type: "file",
    className: "w-4 h-4",
    label: "",
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.profile-image.placeholder",
    ),
    avatarSize: "sm",
    containerClassName: "gap-1",
    changeTranslationKey: "common.form.profile-image.buttons.upload",
    changeButtonIcon: "plus",
    addBtnClassName:
      "bg-transparent shadow-none hover:bg-transparent text-secondary font-bold !text-sm underline border-e ",
    deleteBtnClassName: "border-none text-sm font-bold underline",
    defaultImage: "/images/icons/profile-placeholder.svg",
    deleteTranslationKey: "common.form.profile-image.buttons.delete",
    fallbackText: "U",
  },
  {
    name: "name",
    className: "pt-16",
    type: "text",
    label: t(
      "people.employees-page.add-employee-dialog.form.employee-name.label",
    ),
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.employee-name.placeholder",
    ),
    rightIcon: "Profile",
  },
  {
    name: "email",
    type: "email",
    label: t("people.employees-page.add-employee-dialog.form.email.label"),
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.email.placeholder",
    ),
  },
  {
    name: "phone",
    type: "tel",
    label: t("people.employees-page.add-employee-dialog.form.mobile.label"),
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.mobile.placeholder",
    ),
    rightIcon: "Profile",
  },
  {
    name: "start_date",
    type: "date",
    label: t(
      "people.employees-page.add-employee-dialog.form.registration-date.label",
    ),
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.registration-date.placeholder",
    ),
    className: "w-full",
  },
  {
    name: "department",
    className: "text-base font-normal !text-gray-500",
    type: "select",
    label: t("people.employees-page.add-employee-dialog.form.department.label"),
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.department.placeholder",
    ),
    options: DEPARTMENT_OPTIONS.map((department) => ({
      value: department,
      label: getDepartmentTranslation(department, t),
    })),
  },
  {
    name: "assignments",
    type: "assignments",
    label: t(
      "people.employees-page.add-employee-dialog.form.assignments.label",
    ),
    containerClassName: "mt-6",
  },
  {
    name: "attachments",
    type: "attachments",
    label: t(
      "people.employees-page.add-employee-dialog.form.attachments.label",
    ),
    labelClassName: "sr-only",
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.attachments.placeholder",
    ),
    accept: "*/*",
    multiple: true,
    maxFiles: 10,
    maxSize: 5000000, // 5MB
    containerClassName: "mt-2",
    buttonClassName: "w-full justify-center text-secondary",
    uploadButtonText: t(
      "people.employees-page.add-employee-dialog.form.attachments.button",
    ),
  },
];

export const getCreateLeaveForm = (
  t: TFunction,
): TinputField<CreateLeaveSchemaType>[] => [
  {
    name: "leave_type",
    type: "select",
    label: t(
      "people.employees-page.profile.leaves.create-leave-modal.leave-type",
    ),
    placeholder: t(
      "people.employees-page.profile.leaves.create-leave-modal.select-leave-type",
    ),
    options: Object.values(LEAVE_TYPE).map((leaveType) => ({
      value: leaveType,
      label: t(
        `people.employees-page.profile.leaves.table.leave-types.${leaveType}`,
      ),
    })),
  },
  {
    name: "leave_duration",
    type: "select",
    label: t(
      "people.employees-page.profile.leaves.create-leave-modal.leave-duration",
    ),
    placeholder: t(
      "people.employees-page.profile.leaves.create-leave-modal.select-leave-duration",
    ),
    options: Object.values(LEAVE_DURATION).map((leaveDuration) => ({
      value: leaveDuration,
      label: t(
        `people.employees-page.profile.leaves.table.leave-durations.${leaveDuration}`,
      ),
    })),
  },
  {
    name: "start_date",
    type: "date",
    label: t(
      "people.employees-page.profile.leaves.create-leave-modal.start-date",
    ),
    className: "w-full",
  },
  {
    name: "end_date",
    type: "date",
    label: t(
      "people.employees-page.profile.leaves.create-leave-modal.end-date",
    ),
    className: "w-full",
  },
  {
    name: "reason",
    type: "textarea",
    label: `${t(
      "people.employees-page.profile.leaves.create-leave-modal.reason",
    )} (${t(
      "people.leaves-requests-page.employee-approval-dialog.note-hint",
    )})`,
    placeholder: t(
      "people.employees-page.profile.leaves.create-leave-modal.reason-placeholder",
    ),
    className: "w-full min-h-[100px]",
  },
  {
    name: "documents",
    type: "attachments",
    label: t(
      "people.employees-page.profile.leaves.create-leave-modal.attachments",
    ),
    placeholder: t(
      "people.employees-page.profile.leaves.create-leave-modal.attachments-placeholder",
    ),
    accept: "*/*",
    multiple: true,
    maxFiles: 10,
    maxSize: 5000000, // 5MB
    containerClassName: "mt-2",
    buttonClassName: "w-full justify-center text-secondary",
    uploadButtonText: t(
      "people.employees-page.profile.leaves.create-leave-modal.upload-attachments",
    ),
  },
];

export const getCreateSalaryPackageForm = (
  t: TFunction,
): TinputField<SalaryPackageSchemaType>[] => [
  {
    name: "base_salary",
    type: "text",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("common.form.salary.base_salary.label"),
    placeholder: t("common.form.salary.base_salary.placeholder"),
    formatOnChange: true,
    formatOnBlur: true,
  },
  {
    name: "housing_allowance",
    type: "text",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("common.form.salary.housing_allowance.label"),
    placeholder: t("common.form.salary.housing_allowance.placeholder"),
    formatOnChange: true,
    formatOnBlur: true,
  },
  {
    name: "transportation_allowance",
    type: "text",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("common.form.salary.transportation_allowance.label"),
    placeholder: t("common.form.salary.transportation_allowance.placeholder"),
    formatOnChange: true,
    formatOnBlur: true,
  },
  {
    name: "other_allowances",
    type: "text",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("common.form.salary.other_allowances.label"),
    placeholder: t("common.form.salary.other_allowances.placeholder"),
    formatOnChange: true,
    formatOnBlur: true,
  },
  {
    name: "effective_date",
    type: "date",
    label: t("common.form.salary.effective_date.label"),
    className: "w-full",
  },
  {
    name: "notes",
    type: "textarea",
    className:
      "w-full rounded-[10px] min-h-10 max-h-[138px] placeholder:text-gray-400",
    label: t("common.form.salary.notes.label"),
    labelClassName: "block pt-5",
    placeholder: t("common.form.salary.notes.placeholder"),
  },
];

export const getInlineEditSalaryFields = (
  t: TFunction,
): TinputField<SalaryPackageSchemaType>[] => {
  // Reuse the existing salary package form fields with inline edit styling
  const baseFields = getCreateSalaryPackageForm(t);

  // Filter out notes field as it will be handled separately
  const inlineFields = baseFields.filter((field) => field.name !== "notes");

  // Override styling for inline edit
  return inlineFields.map((field) => ({
    ...field,
    placeholder: field.type === "text" ? "0" : field.placeholder,
    className:
      "w-full h-10 text-sm font-readex_pro" +
      (field.type === "text" ? " text-right" : ""),
  }));
};

export const getNotesField = (
  t: TFunction,
  mode?: "create" | "update",
): TinputField<SalaryPackageSchemaType> => {
  return {
    name: "notes",
    type: "textarea",
    label:
      mode !== "update"
        ? t("common.form.salary.notes.label")
        : t("common.form.salary.notes.label2"),
    placeholder:
      mode !== "update"
        ? t("common.form.salary.notes.placeholder")
        : t("common.form.salary.notes.placeholder2"),
    labelClassName: "block pt-5",
    className: "w-full font-readex_pro",
  };
};

export const getHolidayForm = (
  t: TFunction,
): TinputField<HolidaySchemaType>[] => [
  {
    name: "start_date" as keyof HolidaySchemaType,
    type: "dateRange",
    label: t("settings.holidays.form.dates.label"),
    placeholder: t("settings.holidays.form.dates.placeholder"),
    labelClassName: "font-medium text-base text-black leading-[27px]",
    title: t("settings.holidays.form.dates.modal-title"),
    description: t("settings.holidays.form.dates.modal-description"),
    submitButtonText: t("settings.holidays.form.dates.submit-button"),
  },
  {
    name: "name",
    type: "text",
    label: t("settings.holidays.form.name.label"),
    placeholder: t("settings.holidays.form.name.placeholder"),
  },
];

export const getAddDeviceForm = (
  t: TFunction,
): TinputField<DeviceSchemaType>[] => [
  {
    name: "name",
    type: "text",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("people.devices-page.add-device-dialog.form.device-name.label"),
    placeholder: t(
      "people.devices-page.add-device-dialog.form.device-name.placeholder",
    ),
  },
  {
    name: "adapter_type",
    type: "select",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("people.devices-page.add-device-dialog.form.device-type.label"),
    placeholder: t(
      "people.devices-page.add-device-dialog.form.device-type.placeholder",
    ),
    options: [{ value: "zkteco", label: "ZKTeco" }],
  },
  {
    name: "ip_address",
    type: "ip-address",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("people.devices-page.add-device-dialog.form.ip-address.label"),
    placeholder: t(
      "people.devices-page.add-device-dialog.form.ip-address.placeholder",
    ),
  },
  {
    name: "port",
    type: "port",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("people.devices-page.add-device-dialog.form.port.label"),
    placeholder: t(
      "people.devices-page.add-device-dialog.form.port.placeholder",
    ),
  },
  {
    name: "location",
    type: "select",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("people.devices-page.add-device-dialog.form.location.label"),
    placeholder: t(
      "people.devices-page.add-device-dialog.form.location.placeholder",
    ),
    options: [
      { value: "athar_1", label: "Athar 1" },
      { value: "athar_2", label: "Athar 2" },
    ],
  },
];

export const getDeviceCommandForm = (
  t: TFunction,
): TinputField<DeviceCommandSchemaType>[] => [
  {
    name: "command",
    type: "select",
    className: "placeholder:text-disabled-text",
    labelClassName: "block text-right",
    label: t("people.devices-page.device-commands.form.command.label"),
    placeholder: t(
      "people.devices-page.device-commands.form.command.placeholder",
    ),
    options: [
      { value: "config-set", label: "Config-Set" },
      { value: "restart", label: "Restart" },
      { value: "sync-time", label: "Sync Time" },
      { value: "get-info", label: "Get Info" },
      { value: "clear-data", label: "Clear Data" },
    ],
  },
];
