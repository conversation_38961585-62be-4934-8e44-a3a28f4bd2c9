import { useEffect } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { filterTypeManager } from '@/lib/filter-type-manager';

export const useTableRegistration = <TData>(
  tablePrefix: string,
  columns: ColumnDef<TData>[]
) => {
  console.log(`🔥 useTableRegistration CALLED: ${tablePrefix} with ${columns.length} columns`);

  // Register immediately, not in useEffect
  filterTypeManager.registerTable(tablePrefix, columns as any);

  useEffect(() => {
    console.log(`🔥 useTableRegistration useEffect: ${tablePrefix}`);
    // Also register in useEffect to handle column changes
    filterTypeManager.registerTable(tablePrefix, columns as any);
  }, [tablePrefix, columns]);
};
