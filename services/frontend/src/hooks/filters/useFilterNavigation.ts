import { useRouter, usePathname, useSearchParams } from "next/navigation";
import qs from "qs";
import { createRansack<PERSON><PERSON> } from "@/components/table/filter/ransack-utils";

// Helper to detect table prefix from pathname
const getTablePrefixFromPath = (pathname: string): string | null => {
  if (pathname.includes('/people/employees')) {
    return 'people.employees-page.table';
  }
  if (pathname.includes('/people/devices')) {
    return 'people.devices-page.table';
  }
  return null;
};

export interface FilterNavigation {
  setFilters: (filters: Record<string, any>) => void;
  clearFilters: () => void;
  addFilter: (field: string, operator: string, value: any) => void;
  removeFilter: (field: string, operator: string) => void;
  updateFilter: (field: string, operator: string, value: any) => void;
}

export const useFilterNavigation = (): FilterNavigation => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const navigateWithParams = (params: any, tablePrefix?: string) => {
    const queryString = qs.stringify(params, {
      arrayFormat: 'repeat',  // Creates: param=value1&param=value2
      encode: false,
      addQueryPrefix: false
    });

    const newUrl = queryString ? `${pathname}?${queryString}` : pathname;
    router.push(newUrl);
  };

  const getCurrentParams = () => {
    const currentSearch = searchParams.toString();
    if (!currentSearch) return {};

    return qs.parse(currentSearch, {
      duplicates: 'combine',  // Parses duplicate params back to arrays for processing
      ignoreQueryPrefix: true
    });
  };

  const setFilters = (filters: Record<string, any>) => {
    const currentParams = getCurrentParams();
    const tablePrefix = getTablePrefixFromPath(pathname);

    delete currentParams.filter;

    if (Object.keys(filters).length > 0) {
      currentParams.filter = filters;
    }

    navigateWithParams(currentParams, tablePrefix || undefined);
  };

  const clearFilters = () => {
    const currentParams = getCurrentParams();
    delete currentParams.filter;
    navigateWithParams(currentParams);
  };

  const addFilter = (field: string, operator: string, value: any) => {
    const currentParams = getCurrentParams();
    const filterKey = createRansackKey(field, operator);

    if (!currentParams.filter) {
      currentParams.filter = {};
    }

    (currentParams.filter as Record<string, any>)[filterKey] = value;
    navigateWithParams(currentParams);
  };

  const removeFilter = (field: string, operator: string) => {
    const currentParams = getCurrentParams();
    const filterKey = createRansackKey(field, operator);

    if (currentParams.filter && (currentParams.filter as Record<string, any>)[filterKey]) {
      delete (currentParams.filter as Record<string, any>)[filterKey];

      if (Object.keys(currentParams.filter).length === 0) {
        delete currentParams.filter;
      }
    }

    navigateWithParams(currentParams);
  };

  const updateFilter = (field: string, operator: string, value: any) => {
    addFilter(field, operator, value);
  };

  return {
    setFilters,
    clearFilters,
    addFilter,
    removeFilter,
    updateFilter,
  };
};
