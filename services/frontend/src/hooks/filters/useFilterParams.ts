import { useSearchParams, usePathname } from "next/navigation";
import qs from "qs";
import { parseRansackKey, isArrayOperator, parseFilterValues } from "@/components/table/filter/ransack-utils";

export interface FilterParams {
  filters: Record<string, any>;
  hasFilters: boolean;
  filterCount: number;
  rawFilterParams: URLSearchParams;
}

// We need to detect the table prefix from the current route
// This is a temporary solution until we can pass it explicitly
const getTablePrefixFromPath = (pathname: string): string | null => {
  if (pathname.includes('/people/employees')) {
    return 'people.employees-page.table';
  }
  if (pathname.includes('/people/devices')) {
    return 'people.devices-page.table';
  }
  return null;
};

export const useFilterParams = (): FilterParams => {
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const searchString = searchParams.toString();
  if (!searchString) {
    return {
      filters: {},
      hasFilters: false,
      filterCount: 0,
      rawFilterParams: new URLSearchParams(),
    };
  }

  const allParams = qs.parse(searchString, {
    duplicates: 'combine',
    ignoreQueryPrefix: true
  });

  const filterParams = allParams.filter || {};

  // Filter valid ransack keys
  const validatedFilters: Record<string, any> = {};
  Object.entries(filterParams).forEach(([key, value]) => {
    const parsedKey = parseRansackKey(key);
    if (parsedKey || key.includes("_")) {
      if (parsedKey && isArrayOperator(parsedKey.operator)) return;
      validatedFilters[key] = value;
    }
  });

  // Get table prefix from current path to enable type conversion
  const tablePrefix = getTablePrefixFromPath(pathname);

  // Use universal helper - handles ALL type conversion
  const filters = tablePrefix
    ? parseFilterValues(validatedFilters, tablePrefix)
    : validatedFilters;

  const rawFilterParams = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach(v => rawFilterParams.append(`filter[${key}]`, String(v)));
    } else {
      rawFilterParams.append(`filter[${key}]`, String(value));
    }
  });

  return {
    filters,
    hasFilters: Object.keys(filters).length > 0,
    filterCount: Object.keys(filters).length,
    rawFilterParams,
  };
};
