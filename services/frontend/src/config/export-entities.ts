export interface ExportEntityConfig {
  apiPath: string;
  sessionTokenKey: string;
  subsystem: string;
  defaultFilters?: Record<string, any>;
  supportedFormats?: ExportFormat[];
  displayName: string;
  fileNamePrefix: string;
}

export type ExportFormat = "csv" | "pdf" | "xlsx";

// Simplified - only single subsystem entities for now
export type EntityConfig = ExportEntityConfig;

export const EXPORT_ENTITIES: Record<string, EntityConfig> = {
  employees: {
    apiPath: "/api/employees",
    sessionTokenKey: "people_session_token",
    subsystem: "people",
    defaultFilters: { limit: 1000, page: 1 },
    supportedFormats: ["csv", "pdf", "xlsx"],
    displayName: "Employees",
    fileNamePrefix: "employees",
  },
  leaves: {
    apiPath: "/api/leaves",
    sessionTokenKey: "people_session_token",
    subsystem: "people",
    defaultFilters: { limit: 1000, page: 1, include: "employee.name" },
    supportedFormats: ["csv", "pdf", "xlsx"],
    displayName: "Leaves",
    fileNamePrefix: "leaves",
  },
  "attendance-events": {
    apiPath: "/api/attendance/events",
    sessionTokenKey: "people_session_token",
    subsystem: "people",
    defaultFilters: { limit: 1000, page: 1, include: "employee.name" },
    supportedFormats: ["csv", "pdf", "xlsx"],
    displayName: "Attendance Events",
    fileNamePrefix: "attendance-events",
  },
};

export function getEntityConfig(entity: string): ExportEntityConfig | null {
  return EXPORT_ENTITIES[entity] || null;
}

export function isValidEntity(entity: string): boolean {
  return getEntityConfig(entity) !== null;
}

export function getSupportedFormats(entity: string): ExportFormat[] {
  const config = getEntityConfig(entity);
  return config?.supportedFormats || ["csv", "pdf", "xlsx"];
}

export function getEntityDisplayName(entity: string): string {
  const config = getEntityConfig(entity);
  return config?.displayName || entity;
}
