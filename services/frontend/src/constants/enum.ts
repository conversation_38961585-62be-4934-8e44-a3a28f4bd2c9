export enum Routes {
  Root = "/",
}

export enum Directions {
  RTL = "rtl",
  LTR = "ltr",
}

export enum LANGUAGES {
  ENGLISH = "en",
  ARABIC = "ar",
}

export enum SYSTEM {
  PEOPLE = "people",
  PROCURE = "procure",
  CM = "cm",
}

export const enum PATIENT_STATUS {
  PENDING = "pending",
  INCOMING = "incoming",
  WAITING = "waiting",
  COMPLETED = "completed",
}

// Request status
export const enum REQUEST_STATUS {
  ACCEPTED = "accepted",
  APPROVED = "approved",
  REJECTED = "rejected",
  WAITING = "waiting",
  PENDING = "pending",
  COMPLETED = "completed",
  WITHDRAWN = "withdrawn",
}

export const enum Attendance_Event_type {
  CheckOut = "check_out",
  CheckIn = "check_in",
}
//draft: 0, submitted: 1, approved: 2, paid: 3, rejected: 4
// Salary status
export const enum SALARY_STATUS {
  PAID = "paid",
  REJECTED = "rejected",
  APPROVED = "approved",
  DRAFT = "draft",
  SUBMITTED = "submitted",
}

// Device status
export const enum DEVICE_STATUS {
  ACTIVE = "active",
  INACTIVE = "inactive",
  MAINTENANCE = "maintenance",
  ERROR = "error",
}

// Months Enum
export enum MONTHS {
  JANUARY = "january",
  FEBRUARY = "february",
  MARCH = "march",
  APRIL = "april",
  MAY = "may",
  JUNE = "june",
  JULY = "july",
  AUGUST = "august",
  SEPTEMBER = "september",
  OCTOBER = "october",
  NOVEMBER = "november",
  DECEMBER = "december",
}
