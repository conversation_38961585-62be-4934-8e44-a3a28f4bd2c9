import type { Metada<PERSON> } from "next";
import { NextIntlClientProvider } from "next-intl";
import { Locale, routing } from "@/i18n/routing";
import { notFound } from "next/navigation";
import {
  getMessages,
  getTranslations,
  setRequestLocale,
} from "next-intl/server";
import { Directions, LANGUAGES } from "@/constants/enum";
import { Toaster } from "@/components/ui/toaster";
import { alex, readex_pro } from "@/fonts";
import { UserProvider } from "@/contexts/user-provider";
import { PermissionProvider } from "@/contexts/PermissionContext";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("common.layouts");

  return {
    title: "Athar EMS",
    description: t("root"),
    icons: {
      icon: "/favicon.ico",
    },
  };
}

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: Locale }>;
}>) {
  const { locale } = await params;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  if (!routing.locales.includes(locale as any)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  const isAr = locale === LANGUAGES.ARABIC;

  const messages = await getMessages();

  return (
    <html dir={isAr ? Directions.RTL : Directions.LTR} lang={locale}>
      <body
        className={`${alex.variable} ${readex_pro.variable} font-alex light flex flex-col bg-background-v2`}
      >
        <NextIntlClientProvider locale={locale} messages={messages}>
          <UserProvider>
            <div className="min-h-full">{children}</div>
            <Toaster />
          </UserProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
