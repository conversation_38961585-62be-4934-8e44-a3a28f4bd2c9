"use client";
import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef, TableMeta } from "@tanstack/react-table";
import SortableHeader from "@/components/table/sortable-header";
import ProjectStatus from "@/components/table/patient-status";
import { PATIENT_STATUS } from "@/constants/enum";
import { mapStatusToCanonical } from "@/constants/translations-mapping";
import { PatientsAppointmentsActions } from "./patients-appointments-Actions";
import { Appointment } from "../../../types/appointment-table";

// Define the translation function type
interface TableMetaWithTranslation extends TableMeta<Appointment> {
  t: (key: string) => string;
  onShowDetails: (appointment: Appointment) => void;
}

// Helper function to get meta data
const getMeta = (table: any) => table.options.meta as TableMetaWithTranslation;

export const columns: ColumnDef<Appointment>[] = [
  // Selection Checkbox
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#ACB5BB] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },

  {
    id: "number",
    accessorKey: "number",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => getMeta(table).t("cm.table.columns.number"),
    cell: ({ row }) => {
      const rowIndex = row.getValue("number");
      return rowIndex;
    },
  },

  // Patient Name
  {
    accessorKey: "patientName",
    header: ({ table }) => getMeta(table).t("cm.table.columns.patientName"),
    meta: { filterVariant: "select" },
  },

  // Sorting
  {
    accessorKey: "time",
    sortingFn: "datetime",
    header: ({ column, table }) => (
      <SortableHeader
        column={column}
        table={table}
        title={getMeta(table).t("cm.table.columns.time")}
      />
    ),
  },

  // Description
  {
    accessorKey: "description",
    header: ({ table }) => (
      <div>{getMeta(table).t("cm.table.columns.description")}</div>
    ),
    cell: ({ row }) => (
      <p className="text-xs font-normal">{row.original.description}</p>
    ),
  },

  // Status Column with Sorting
  {
    accessorKey: "status",
    header: ({ column, table }) => (
      <SortableHeader
        column={column}
        table={table}
        title={getMeta(table).t("cm.table.columns.status")}
      />
    ),
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const status = row.getValue("status") as PATIENT_STATUS;
      const final = mapStatusToCanonical(status);
      return (
        <ProjectStatus
          status={final}
          label={t(`common.Table.body.patient-status.${final}`)}
        />
      );
    },
  },

  // Actions Column
  {
    accessorKey: "actions",
    id: "actions",
    enableHiding: false,
    header: ({ table }) => getMeta(table).t("cm.table.columns.actions"),
    cell: ({ row, table }) => {
      const { onShowDetails } = getMeta(table);
      return (
        <PatientsAppointmentsActions row={row} onShowDetails={onShowDetails} />
      );
    },
  },
];
