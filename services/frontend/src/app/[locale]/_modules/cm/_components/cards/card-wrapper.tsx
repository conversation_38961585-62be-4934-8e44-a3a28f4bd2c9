import { iconMap } from "@/constants";
import React from "react";
import { fetchCardData } from "../../services/api/health-card";
import { getTranslations } from "next-intl/server";
import { TFunction } from "@/types";

const CardWrapper = async () => {
  const { appointmentCount, patientCount, videoCount } = await fetchCardData();
  const t = (await getTranslations()) as TFunction;
  return (
    <>
      <Card
        title={t("cm.card.appointments")}
        value={appointmentCount}
        type="Appointment"
      />
      <Card title={t("cm.card.patients")} value={patientCount} type="Patient" />
      <Card
        title={t("cm.card.videoConsultations")}
        value={videoCount}
        type="Video"
      />
    </>
  );
};

export default CardWrapper;

type TCard = {
  title: string;
  value: number | string;
  type: "Appointment" | "Patient" | "Video";
};

export const Card = ({ title, type, value }: TCard) => {
  const Icon = iconMap[type];
  return (
    <div className="rounded-xl border border-neutral-100 bg-white py-6 px-3 xl:px-5 flex gap-3 xl:gap-5 items-center max-h-[106px]">
      {Icon ? React.createElement(Icon, { className: "shrink-0" }) : null}
      <div className="flex flex-col">
        <h3 className="ml-2 font-bold leading-9 text-[24px] h-9">{value}</h3>
        <p className="text-sm font-semibold leading-[22px] h-[22px]">{title}</p>
      </div>
    </div>
  );
};
