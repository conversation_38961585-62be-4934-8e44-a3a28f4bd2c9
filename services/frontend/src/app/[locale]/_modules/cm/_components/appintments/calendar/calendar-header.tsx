"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import AvailabilityForm from "./availability-form";
import dynamic from "next/dynamic";
import Loader from "@/components/loader";

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
  },
);
const CalendarHeader: React.FC = () => {
  const t = useTranslations() as TFunction;
  const [availableTimes, setAvailableTimes] = useState<boolean>(false);

  return (
    <div className="max-md:pt-2 md:pb-5 flex max-md:flex-col items-start md:items-center md:justify-between gap-4">
      <h2 className="text-secondary max-w-[301px] leading-[120%]">
        this is a sample text to change
      </h2>
      <Button
        onClick={() => setAvailableTimes((prev) => !prev)}
        variant={"outline"}
        className="min-w-[134px] min-h-12 shadow-none font-semibold text-base max-md:mb-8"
      >
        {t("cm.appointment-calendar.your-availability-btn")}
      </Button>
      {availableTimes && (
        <ResponsiveDialog
          open={availableTimes}
          onOpenChange={setAvailableTimes}
          header={
            <>
              <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
                {t("cm.appointment-calendar.availabilitySelectTitle")}
              </DialogTitle>
              <DialogDescription className="sr-only">
                {t("cm.appointment-calendar.dialog-description")}
              </DialogDescription>
            </>
          }
          closeBtnStyle="top-[21px]"
        >
          <AvailabilityForm />
        </ResponsiveDialog>
      )}
    </div>
  );
};

export default CalendarHeader;
