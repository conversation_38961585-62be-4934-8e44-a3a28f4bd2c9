import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LANGUAGES } from "@/constants/enum";
import { Row } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { CiMenuKebab } from "react-icons/ci";
import { Eye } from "../../../../../../../../public/images/icons";
import { Locale } from "@/i18n/routing";

type PatientsAppointmentsActionsProps<TData> = {
  row: Row<TData>;
  onShowDetails: (value: TData) => void;
};

const PatientsAppointmentsActions = <TData,>({
  row,
  onShowDetails,
}: PatientsAppointmentsActionsProps<TData>) => {
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 w-8 p-0 border border-[#EEEEEE] rounded-lg"
        >
          <span className="sr-only">Open menu</span>
          <CiMenuKebab className="stroke-[0.5px]" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        lang={locale}
        className="w-60 max-w-60 rounded-2xl font-semibold text-sm text-start text-[#727A90]"
        align={isAr ? "start" : "end"}
      >
        <DropdownMenuItem
          className="flex rtl:flex-row-reverse items-center min-h-10 group rounded-2xl"
          onClick={() => onShowDetails(row.original)}
        >
          <span>
            <Eye className="!w-6 !h-6 stroke-[#6B7280] group-hover:stroke-secondary" />
          </span>
          <span> {t("cm.table.actions.Action1")}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export { PatientsAppointmentsActions };
