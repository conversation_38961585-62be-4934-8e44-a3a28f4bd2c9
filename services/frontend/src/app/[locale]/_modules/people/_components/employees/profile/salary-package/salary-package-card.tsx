"use client";

import React from "react";
import { useTranslations, useLocale } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import FormattedCurrency from "@/components/formatted-currency";
import { TSalaryPackageData } from "../../../../type/salary-package";
import { formatDate } from "@/lib/dateFormatter";
import { Locale } from "@/i18n/routing";
import { cn } from "@/lib/utils";
import Image from "next/image";

type SalaryPackageCardProps = {
  salaryPackage: TSalaryPackageData | null;
};

const CurrencyItem = ({ label, amount }: { label: string; amount: number }) => (
  <Card className="shadow-sm max-h-[104px]">
    <CardContent className="text-start p-5">
      <p className="text-gray-500 font-medium leading-6 text-sm mb-2">
        {label}:
      </p>
      <FormattedCurrency
        wrapperStyle="justify-start"
        amount={amount}
        numberStyle="text-xl font-semibold text-black leading-"
        currencyStyle="hidden"
      />
    </CardContent>
  </Card>
);

export const SalaryPackageCard = ({
  salaryPackage,
}: SalaryPackageCardProps) => {
  const t = useTranslations();
  const locale = useLocale() as Locale;

  if (!salaryPackage) {
    return (
      <div>
        <div className="relative w-80 h-40 mx-auto">
          <Image
            fill
            priority={true}
            src="/images/EmptyState.png"
            className="object-cover"
            alt="image descripe empty salary package"
          />
        </div>
        <div className="text-center mt-2">
          <p className="text-black text-sm leading-6 font-medium">
            {t("people.salary-package.no-package")}
          </p>
          <p className="text-gray-500 text-xs mt-2">
            {t("people.salary-package.no-package-description")}
          </p>
        </div>
      </div>
    );
  }

  const { attributes } = salaryPackage;

  const baseSalary = Number(attributes.base_salary) || 0;
  const housingAllowance = Number(attributes.housing_allowance) || 0;
  const transportationAllowance =
    Number(attributes.transportation_allowance) || 0;
  const otherAllowances = Number(attributes.other_allowances) || 0;

  const totalPackage =
    baseSalary + housingAllowance + transportationAllowance + otherAllowances;

  const isActive = attributes.active ? "active" : "inactive";

  return (
    <div className="flex flex-col gap-4">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Total Package Summary */}
        <Card className="border border-gray-200 rounded-2xl shadow-sm relative">
          <CardContent className="p-5 grid grid-cols-2 gap-10">
            <div className="flex flex-col items-start gap-4">
              <p className="text-gray-600 text-sm mb-2">
                {t("people.salary-package.total-package")}
              </p>
              <FormattedCurrency
                wrapperStyle="justify-start"
                amount={totalPackage}
                numberStyle="font-bold text-2xl text-secondary"
              />
            </div>

            <div className="justify-self-end sticky sm:absolute end-6 top-6">
              <Badge
                className={cn(
                  "text-xs h-8 min-w-20 flex justify-center items-center",
                  isActive === "active"
                    ? "text-secondary bg-success-50"
                    : "text-red-500 bg-red-50",
                )}
              >
                {t(`common.form.status.options.${isActive}`)}
                {attributes.status}
              </Badge>
            </div>

            {/* Effective Period */}
            <div className="flex flex-col gap-4 max-sm:col-span-2">
              <p className="text-gray-500 text-sm mb-2">
                {t("people.salary-package.effective-period")}
              </p>
              <p className="text-xl font-semibold text-gray-700">
                {formatDate(attributes.effective_date, locale)} -{" "}
                {attributes.end_date
                  ? formatDate(attributes.end_date, locale)
                  : t("people.salary-package.ongoing")}
              </p>
            </div>
            <div className="col-span-2">
              <p className="text-gray-500 font-medium leading-6 text-sm mb-2">
                {t("common.form.salary.notes.label")}:
              </p>
              <p className="text-gray-700 rounded-lg">{attributes.notes}</p>
            </div>
          </CardContent>
        </Card>

        {/* Breakdown: Base + Allowances */}
        <div className="grid grid-cols-2 gap-4">
          <CurrencyItem
            label={t("common.form.salary.base_salary.label")}
            amount={baseSalary}
          />
          <CurrencyItem
            label={t("common.form.salary.housing_allowance.label")}
            amount={housingAllowance}
          />
          <CurrencyItem
            label={t("common.form.salary.other_allowances.label")}
            amount={otherAllowances}
          />
          <CurrencyItem
            label={t("common.form.salary.transportation_allowance.label")}
            amount={transportationAllowance}
          />
        </div>
      </div>
    </div>
  );
};
