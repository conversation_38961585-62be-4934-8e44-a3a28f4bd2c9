"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef, Table, TableMeta } from "@tanstack/react-table";
import { mapSalaryStatusToCanonical } from "@/constants/translations-mapping";
import { Locale } from "@/i18n/routing";
import { TFunction } from "@/types";
import FormattedCurrency from "@/components/formatted-currency";
import SalaryStatus from "@/components/status/salary-status";
import { SALARY_STATUS } from "@/constants/enum";

import { EmployeeSalaryActions } from "../employees/profile/salary/employee-salary-actions";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { InfoIcon } from "lucide-react";
import { SalaryCalculation } from "../../type/employees-salaries";
import { findEmployeeById } from "../../utils/find-employee";
import { TIncludedEmployee } from "../../type/employee-leaves";
import { formatDate } from "@/lib/dateFormatter";

// Define the shared translation function type
interface TableMetaWithTranslation extends TableMeta<SalaryCalculation> {
  t: TFunction;
  locale?: Locale;
  onApproveSalary?: (salaryId: string) => void;
  onRejectSalary?: (salaryId: string) => void;
  onSubmitSalary?: (salaryId: string) => void;
  employeesData?: TIncludedEmployee[];
  isApproving?: boolean;
  isRejecting?: boolean;
  isSubmitting?: boolean;
  onShowDetails?: (employeeSalary: SalaryCalculation) => void;
  onShowEditNote?: (employeeSalary: SalaryCalculation) => void;
}

// Helper function to get meta data
const getMeta = (table: Table<SalaryCalculation>) =>
  table.options.meta as TableMetaWithTranslation;

// Base columns that are shared between both tables
const baseColumns: ColumnDef<SalaryCalculation>[] = [
  // Selection Checkbox
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(Boolean(value))}
        aria-label="Select row"
      />
    ),
  },
  // Period
  {
    accessorKey: "period",
    header: ({ table }) =>
      getMeta(table).t(
        "people.employees-page.profile.salary.table.columns.period",
      ),
    meta: { filterVariant: "select" },
    size: 100,
    cell: ({ row, table }) => {
      const period = row.original.attributes.period;
      const { locale } = getMeta(table);
      return (
        <p className="text-sm font-semibold text-black">
          {formatDate(period, locale || "en")}
        </p>
      );
    },
  },
  // Total Hours
  {
    accessorKey: "totalHours",
    header: ({ table }) =>
      getMeta(table).t(
        "people.employees-page.profile.salary.table.columns.totalHours",
      ),
    meta: { filterVariant: "select" },
    size: 100,
    cell: ({ row }) => {
      const totalHours = row.original.attributes.total_hours;
      return (
        <p className="text-sm font-semibold text-black">{totalHours || "-"}</p>
      );
    },
  },
  // Gross Salary
  {
    accessorKey: "grossSalary",
    header: ({ table }) =>
      getMeta(table).t(
        "people.employees-page.profile.salary.table.columns.grossSalary",
      ),
    meta: { filterVariant: "select" },
    size: 100,
    cell: ({ row }) => (
      <FormattedCurrency
        amount={Number(row.original.attributes.gross_salary)}
        numberStyle="text-sm font-semibold text-black"
      />
    ),
  },
  // Net Salary
  {
    accessorKey: "netSalary",
    header: ({ table }) =>
      getMeta(table).t(
        "people.employees-page.profile.salary.table.columns.netSalary",
      ),
    meta: { filterVariant: "select" },
    size: 100,
    cell: ({ row }) => (
      <FormattedCurrency
        amount={Number(row.original.attributes.net_salary)}
        numberStyle="text-sm font-semibold text-black"
      />
    ),
  },
  // Total Deductions with Tooltip
  {
    accessorKey: "totalDeductions",
    header: ({ table }) =>
      getMeta(table).t(
        "people.employees-page.profile.salary.table.columns.totalDeductions",
      ),
    meta: { filterVariant: "select" },
    size: 100,
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const deductions = row.original.attributes.deductions;
      const totalDeductions = row.original.attributes.total_deductions;

      return (
        <div className="flex justify-center text-center items-center gap-1">
          <FormattedCurrency
            amount={Number(totalDeductions)}
            numberStyle="text-sm font-semibold text-black"
          />
          <DeductionsTooltip
            totalDeductions={totalDeductions}
            deductions={deductions}
            t={t}
          />
        </div>
      );
    },
  },
  // Status
  {
    accessorKey: "status",
    header: ({ table }) =>
      getMeta(table).t(
        "people.employees-page.profile.salary.table.columns.status",
      ),
    meta: { filterVariant: "select" },
    size: 100,
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const status = row.original.attributes.status;
      const canonicalStatus = mapSalaryStatusToCanonical(status);

      // Get the translated label from the translation files
      const translatedLabel = t(
        `common.status.salary-status.${canonicalStatus}`,
      );

      return (
        <div className="flex items-center text-center justify-center">
          <SalaryStatus
            status={canonicalStatus as SALARY_STATUS}
            label={translatedLabel}
          />
        </div>
      );
    },
  },
  // Actions
  {
    id: "actions",
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t(
          "people.employees-page.profile.salary.table.columns.actions",
        )}
      </div>
    ),
    cell: ({ row, table }) => {
      const {
        onApproveSalary,
        onRejectSalary,
        onSubmitSalary,
        isApproving,
        isRejecting,
        isSubmitting,
      } = getMeta(table);
      return (
        <div className="flex justify-center">
          <EmployeeSalaryActions
            row={row}
            onApproveSalary={onApproveSalary}
            onRejectSalary={onRejectSalary}
            onSubmitSalary={onSubmitSalary}
            isApproving={isApproving}
            isRejecting={isRejecting}
            isSubmitting={isSubmitting}
          />
        </div>
      );
    },
  },
];

// Export columns for employee profile salary table
export const employeeSalaryColumns: ColumnDef<SalaryCalculation>[] =
  baseColumns;

// Export columns for main employees salaries table with employee name column
export const employeesSalariesColumns: ColumnDef<SalaryCalculation>[] = [
  // First add the selection checkbox
  baseColumns[0],
  // Then add the employee name column
  {
    accessorKey: "employeeName",
    header: ({ table }) =>
      getMeta(table).t(
        "people.employees-salaries-page.table.columns.employeeName",
      ),
    meta: { filterVariant: "select" },
    size: 100,
    cell: ({ row, table }) => {
      const { employeesData } = getMeta(table);
      const employeeId = row.original.relationships?.employee?.data?.id;
      const employee =
        employeesData && employeeId
          ? findEmployeeById(employeesData, employeeId)
          : null;
      return <div className="flex justify-center">{employee?.name || "-"}</div>;
    },
  },
  // Then add all the remaining columns
  ...baseColumns.slice(1),
];

// Shared deductions tooltip component
const DeductionsTooltip = ({
  totalDeductions,
  deductions,
  t,
}: {
  totalDeductions: SalaryCalculation["attributes"]["total_deductions"];
  deductions: SalaryCalculation["attributes"]["deductions"];
  t: TFunction;
}) => {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={100}>
        <TooltipTrigger asChild>
          <InfoIcon className="h-4 w-4 text-secondary hover:text-secondary/80 cursor-help transition-colors" />
        </TooltipTrigger>
        <TooltipContent
          className="p-4 min-w-[250px] max-w-[300px] bg-white border border-border shadow-md rounded-md"
          sideOffset={5}
        >
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-secondary mb-2 pb-1 border-b border-border">
              {t("people.employees-page.profile.salary.table.deductions.title")}
            </h4>
            <div className="space-y-2 text-xs">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">
                  {t(
                    "people.employees-page.profile.salary.table.deductions.leave",
                  )}
                  :
                </span>
                <FormattedCurrency
                  amount={Number(deductions.leave)}
                  numberStyle="font-medium text-black"
                />
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">
                  {t(
                    "people.employees-page.profile.salary.table.deductions.attendance",
                  )}
                  :
                </span>
                <FormattedCurrency
                  amount={Number(deductions.attendance)}
                  numberStyle="font-medium text-black"
                />
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">
                  {t(
                    "people.employees-page.profile.salary.table.deductions.other",
                  )}
                  :
                </span>
                <FormattedCurrency
                  amount={Number(deductions.other)}
                  numberStyle="font-medium text-black"
                />
              </div>

              <div className="my-1 border-t border-dashed border-gray-200"></div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600">
                  {t(
                    "people.employees-page.profile.salary.table.deductions.income_tax",
                  )}
                  :
                </span>
                <FormattedCurrency
                  amount={Number(deductions.income_tax)}
                  numberStyle="font-medium text-black"
                />
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">
                  {t(
                    "people.employees-page.profile.salary.table.deductions.social_security",
                  )}
                  :
                </span>
                <FormattedCurrency
                  amount={Number(deductions.social_security)}
                  numberStyle="font-medium text-black"
                />
              </div>

              <div className="mt-2 pt-1 border-t border-border">
                <div className="flex justify-between items-center">
                  <span className="font-semibold text-secondary">
                    {t(
                      "people.employees-page.profile.salary.table.deductions.total",
                    )}
                    :
                  </span>
                  <FormattedCurrency
                    amount={Number(totalDeductions)}
                    numberStyle="font-semibold text-secondary"
                  />
                </div>
              </div>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
