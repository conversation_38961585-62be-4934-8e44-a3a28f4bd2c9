"use client";

import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { RowSelectionState } from "@tanstack/react-table";
import { useEmployeeLeaveDetails } from "../../../../hooks/employees/useEmployeeLeaveDetails";
import { useEmployeeLeaveMutations } from "../../../../hooks/employees/useEmployeeLeaveMutations";
import { employeeLeavesColumns } from "./employee-leaves-columns";
import { useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import dynamic from "next/dynamic";
import LoaderPortal from "@/components/loader/loader-portal";

const CreateLeaveModal = dynamic(
  () => import("./create-leave-modal").then((mod) => mod.CreateLeaveModal),
  {
    loading: () => (
      <LoaderPortal
        size={75}
        borderWidth={4}
        overlayColor="#000"
        overlayClassName="bg-opacity-50"
      />
    ),
    ssr: false,
  },
);

type EmployeeLeavesTableProps = {
  employeeId: string;
};

export const EmployeeLeavesTable = ({
  employeeId,
}: EmployeeLeavesTableProps) => {
  const t = useTranslations();
  const locale = useLocale();
  const searchParams = useSearchParams();
  const limit = searchParams.get("limit") ?? "5";
  const page = searchParams.get("page") ?? "1";
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Fetch leave details for the employee
  const {
    leaveDetails,
    pagination,
    isLoading,
    error,
    mutate: mutateData,
  } = useEmployeeLeaveDetails(employeeId, Number(page), Number(limit));

  // Get mutations for employee leaves
  const {
    withdrawLeave,
    updateLeaveDates,
    acceptLeave,
    rejectLeave,
    isWithdrawing,
    isUpdatingDates,
    isAccepting,
    isRejecting,
  } = useEmployeeLeaveMutations({
    employeeId,
    page,
    limit,
  });

  // Create leave button component
  const CreateLeaveButton = (
    <Button
      onClick={() => setIsCreateModalOpen(true)}
      className="sm:min-w-[169px] min-h-12 max-h-12 shadow-none font-semibold text-base flex gap-2 items-center"
    >
      <PlusCircle className="h-5 w-5" />
      {t("people.employees-page.profile.leaves.create-leave")}
    </Button>
  );

  return (
    <>
      <DataTable
        data={leaveDetails}
        dataCount={pagination?.count}
        columns={employeeLeavesColumns}
        title={t("people.employees-page.profile.leaves.table.title")}
        meta={{
          t,
          locale: locale,
          onWithdrawLeave: withdrawLeave,
          onUpdateDates: updateLeaveDates,
          onAcceptLeave: acceptLeave,
          onRejectLeave: rejectLeave,
          isWithdrawing,
          isUpdatingDates,
          isAccepting,
          isRejecting,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.employees-page.profile.leaves.table"
        isLoading={isLoading}
        error={error}
        hideSearch={true}
        hideFilters={true}
        headerActions={CreateLeaveButton}
      />

      {/* Pagination */}
      <div className="w-full pt-[18px]">
        <PaginationWithLinks
          page={Number(page)}
          pageSize={Number(limit)}
          totalCount={pagination?.count || 0}
          firstLastCounts={{
            firstCount: pagination?.from || 1,
            lastCount: pagination?.to || 5,
          }}
          isLoading={isLoading}
          isDisabled={!leaveDetails?.length}
          pageSizeSelectOptions={{
            pageSizeOptions: [5, 10, 25, 30, 45, 50],
            pageSizeSearchParam: "limit",
          }}
        />
      </div>

      {/* Create Leave Modal */}
      {isCreateModalOpen && (
        <CreateLeaveModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          employeeId={employeeId}
          onSuccess={() => {
            mutateData();
          }}
        />
      )}
    </>
  );
};
