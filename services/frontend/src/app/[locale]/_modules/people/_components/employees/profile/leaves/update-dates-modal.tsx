"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import ResponsiveDialog from "@/components/responsive-dialog";
import { DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { DateRange } from "react-day-picker";
import { ar, enUS } from "date-fns/locale";
import { Locale } from "@/i18n/routing";
import { useLocale, useTranslations } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import { formatDate } from "@/lib/dateFormatter";
import { createDateWithoutTimezoneIssue } from "@/lib/utils";

type UpdateDatesModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (startDate: Date, endDate: Date) => void;
  initialStartDate?: string | Date;
  initialEndDate?: string | Date;
  isLoading: boolean;
  mode?: "update" | "create";
  title?: string;
  description?: string;
  submitButtonText?: string;
};

export function UpdateDatesModal({
  isOpen,
  onClose,
  onUpdate,
  initialStartDate,
  initialEndDate,
  isLoading,
  mode = "update",
  title,
  description,
  submitButtonText,
}: UpdateDatesModalProps) {
  const initialStartDateObj = createDateWithoutTimezoneIssue(initialStartDate);
  const initialEndDateObj = createDateWithoutTimezoneIssue(initialEndDate);

  // State for selected date range
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: initialStartDateObj,
    to: initialEndDateObj,
  });

  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();

  // Validation errors
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = () => {
    // Reset error
    setError(null);

    // Validate dates
    if (!dateRange?.from || !dateRange?.to) {
      setError(
        t(
          "people.employees-page.profile.leaves.update-dates.errors.dates-required",
        ),
      );
      return;
    }

    // Validate start date is not in the past
    const today = new Date(new Date().setHours(0, 0, 0, 0));

    // For create mode, both start and end dates must be today or in the future
    if (mode === "create" && dateRange.from < today) {
      setError(
        t("people.employees-page.profile.leaves.update-dates.errors.past-date"),
      );
      return;
    }

    // For update mode, we allow dates in the past but not more than 90 days
    if (mode === "update") {
      // Validate that start date is not more than 90 days in the past
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
      ninetyDaysAgo.setHours(0, 0, 0, 0);

      if (dateRange.from < ninetyDaysAgo) {
        setError(
          "Start date cannot be more than 3 months (90 days) in the past",
        );
        return;
      }
    }

    // Call the update function
    onUpdate(dateRange.from, dateRange.to);
  };

  // Get appropriate title and description based on mode
  const modalTitle =
    title ||
    (mode === "update"
      ? t("people.employees-page.profile.leaves.update-dates.title")
      : t(
          "people.employees-page.profile.leaves.create-leave-modal.select-dates",
        ));

  const modalDescription =
    description ||
    (mode === "update"
      ? t("people.employees-page.profile.leaves.update-dates.description")
      : t(
          "people.employees-page.profile.leaves.create-leave-modal.select-dates-description",
        ));

  const buttonText =
    submitButtonText ||
    (mode === "update"
      ? t("people.employees-page.profile.leaves.update-dates.update-button")
      : t(
          "people.employees-page.profile.leaves.create-leave-modal.select-dates-button",
        ));

  // Create header content
  const headerContent = (
    <div className="px-6 py-5 border-b bg-white sticky top-0 z-10 rounded-2xl">
      <DialogTitle className="text-lg font-semibold">{modalTitle}</DialogTitle>
      <DialogDescription className="text-sm text-gray-500 mt-1">
        {modalDescription}
      </DialogDescription>
    </div>
  );

  // Create footer content
  const footerContent = (
    <div className="flex justify-center md:justify-end gap-3 max-md:px-6 pt-4 border-t bg-white sticky bottom-0 left-0 right-0 z-10 shadow-sm">
      <Button
        variant="outline"
        onClick={onClose}
        disabled={isLoading}
        className="min-w-[100px] min-h-12 w-3/6 md:w-10"
      >
        {t("common.buttonText.cancel")}
      </Button>
      <Button
        onClick={handleSubmit}
        disabled={isLoading}
        className="min-w-[100px] min-h-12 w-3/6 md:w-2/6"
      >
        {isLoading
          ? t("people.employees-page.profile.leaves.update-dates.updating")
          : buttonText}
      </Button>
    </div>
  );

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      header={headerContent}
      className="max-w-[700px] max-h-[calc(100vh-50px)] h-[calc(100vh-50px)] rounded-2xl"
    >
      {/* Calendar container */}
      <div className="w-full mb-6">
        <div className="border rounded-md p-4 bg-white w-full">
          <Calendar
            locale={isAr ? ar : enUS}
            mode="range"
            selected={dateRange}
            onSelect={setDateRange}
            numberOfMonths={2}
            disabled={(date) =>
              date < new Date(new Date().setHours(0, 0, 0, 0))
            }
            showOutsideDays={false}
            classNames={{
              root: "w-full",
              months:
                "flex gap-3 flex-col md:flex-row space-y-4 md:space-x-4 md:space-y-0 w-full justify-center",
              month: "space-y-4 w-full",
              caption: "flex justify-center pt-1 relative items-center",
              caption_label: "text-sm font-medium",
              nav: "space-x-1 flex items-center",
              nav_button:
                "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
              nav_button_previous: "absolute left-1",
              nav_button_next: "absolute right-1",
              table: "w-full border-collapse space-y-1",
              head_row: "flex w-full justify-between",
              head_cell:
                "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem] text-center",
              row: "flex w-full mt-2 justify-between",
              cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
              day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100",
              day_selected:
                "bg-secondary text-primary-foreground hover:bg-primary rounded-sm hover:text-primary-foreground focus:bg-secondary focus:text-primary-foreground",
              day_today: "bg-gray-200 rounded-sm text-accent-foreground",
              day_outside: "text-muted-foreground opacity-50",
              day_disabled: "text-muted-foreground opacity-50",
              day_range_middle:
                "aria-selected:bg-accent aria-selected:text-accent-foreground",
              day_hidden: "invisible",
            }}
          />
        </div>

        {/* Error message */}
        {error && (
          <div className="w-full mt-4">
            <p className="text-sm text-red-500 p-2 bg-red-50 rounded-md border border-red-100">
              {error}
            </p>
          </div>
        )}
      </div>

      {/* Date summary - spans full width */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
        <h3 className="text-sm font-medium mb-3">
          {t(
            "people.employees-page.profile.leaves.update-dates.selected-range",
          )}
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-gray-500">
              {t(
                "people.employees-page.profile.leaves.update-dates.start-date",
              )}
              :
            </p>
            <p className="text-sm font-medium">
              {dateRange?.from
                ? formatDate(dateRange.from, locale)
                : t("people.employees-page.profile.leaves.update-dates.none")}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500">
              {t("people.employees-page.profile.leaves.update-dates.end-date")}:
            </p>
            <p className="text-sm font-medium">
              {dateRange?.to
                ? formatDate(dateRange.to, locale)
                : t("people.employees-page.profile.leaves.update-dates.none")}
            </p>
          </div>
        </div>
      </div>
      {footerContent}
    </ResponsiveDialog>
  );
}
