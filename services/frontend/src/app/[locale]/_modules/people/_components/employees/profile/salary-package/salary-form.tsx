"use client";

import React from "react";
import { Form } from "@/components/ui/form";
import { getInlineEditSalaryFields } from "@/hooks/form-fields/people-forms";
import { useSalaryForm } from "./hooks/use-salary-form";
import { SalaryFieldRow } from "./components/salary-field-row";
import { SalaryTotalDisplay } from "./components/salary-total-display";
import { FormFooterButtons } from "./components/form-footer-buttons";
import { NotesSection } from "./components/notes-section";
import { TSalaryPackageData } from "../../../../type/salary-package";

type SalaryFormProps = {
  employeeId: string;
  onClose: () => void;
  existingPackage?: TSalaryPackageData | null;
  onSalaryPackageUpdated?: (updatedPackage: TSalaryPackageData) => void;
  onSalaryPackageCreated?: (newPackage: TSalaryPackageData) => void;
  mode?: "create" | "update";
};

const SalaryForm: React.FC<SalaryFormProps> = ({
  employeeId,
  onClose,
  existingPackage,
  onSalaryPackageUpdated,
  onSalaryPackageCreated,
  mode = existingPackage ? "update" : "create",
}) => {
  const {
    form,
    formRef,
    isPending,
    editingFields,
    toggleFieldEdit,
    handleSubmit,
    calculateTotal,
    t,
    locale,
  } = useSalaryForm({
    employeeId,
    mode,
    existingPackage,
    onClose,
    onSalaryPackageCreated,
    onSalaryPackageUpdated,
  });

  const salaryFields = getInlineEditSalaryFields(t);

  return (
    <Form {...form}>
      <form
        ref={formRef}
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-6"
      >
        {/* Editable Salary Fields */}
        <div className="border rounded-2xl p-6 min-h-[calc(93vh-230px)]">
          <div className="space-y-5 border-b border-gray-100">
            {salaryFields.map((field, index) => (
              <SalaryFieldRow
                key={field.name}
                field={field}
                index={index}
                totalFields={salaryFields.length}
                form={form}
                isPending={isPending}
                isEditing={editingFields.has(field.name || "")}
                onToggleEdit={toggleFieldEdit}
                t={t}
                locale={locale}
              />
            ))}
          </div>

          {/* Notes Section */}
          <NotesSection mode={mode} form={form} isPending={isPending} t={t} />

          {/* Total Package Display */}
          <SalaryTotalDisplay totalAmount={calculateTotal()} t={t} />
        </div>

        {/* Form Footer Buttons */}
        <FormFooterButtons
          isPending={isPending}
          isDirty={form.formState.isDirty}
          onClose={onClose}
          t={t}
        />
      </form>
    </Form>
  );
};

export default SalaryForm;
