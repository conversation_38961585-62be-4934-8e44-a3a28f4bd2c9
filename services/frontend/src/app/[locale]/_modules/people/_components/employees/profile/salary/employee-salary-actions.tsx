"use client";

import { Row } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { Locale } from "@/i18n/routing";
import { mapSalaryStatusToCanonical } from "@/constants/translations-mapping";
import { SalaryCalculation } from "../../../../type/employees-salaries";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";
import { LANGUAGES, SALARY_STATUS } from "@/constants/enum";
import { PermissionEnum } from "@/enums/Permission";
import { usePermission } from "@/contexts/PermissionContext";

type EmployeeSalaryActionsProps<TData> = {
  row: Row<TData>;
  onApproveSalary?: (salaryId: string) => void;
  onRejectSalary?: (salaryId: string) => void;
  onSubmitSalary?: (salaryId: string) => void;
  isApproving?: boolean;
  isRejecting?: boolean;
  isSubmitting?: boolean;
};

export const EmployeeSalaryActions = <TData extends SalaryCalculation>({
  row,
  onApproveSalary,
  onRejectSalary,
  onSubmitSalary,
  isApproving,
  isRejecting,
  isSubmitting,
}: EmployeeSalaryActionsProps<TData>) => {
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();
  const status = row.original.attributes.status;
  const canonicalStatus = mapSalaryStatusToCanonical(status);
  const { hasPermission } = usePermission();

  // Check if the status is draft (only show submit option)
  const isDraft = canonicalStatus === SALARY_STATUS.DRAFT;

  // For salaries, only SUBMITTED status can be approved/rejected
  const canBeApprovedOrRejected = canonicalStatus === SALARY_STATUS.SUBMITTED;

  // Check permissions
  const canSubmitSalary = hasPermission(
    PermissionEnum.SUBMIT_SALARY_CALCULATION,
  );
  const canApproveSalary = hasPermission(
    PermissionEnum.APPROVE_SALARY_CALCULATION,
  );

  // Check if actions are available based on permissions and status
  const hasSubmitAction = isDraft && onSubmitSalary && canSubmitSalary;
  const hasApproveAction =
    canBeApprovedOrRejected && onApproveSalary && canApproveSalary;
  const hasRejectAction =
    canBeApprovedOrRejected && onRejectSalary && canApproveSalary;

  // Check if there are any actions available
  const hasActions = hasSubmitAction || hasApproveAction || hasRejectAction;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 w-8 p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
          disabled={isApproving || isRejecting || !hasActions}
        >
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={isAr ? "start" : "end"} className="w-[160px]">
        {/* Submit option for draft status */}
        {hasSubmitAction && (
          <DropdownMenuItem
            onClick={() => onSubmitSalary!(row.original.id)}
            disabled={isSubmitting}
            className="cursor-pointer font-medium text-blue-600 rtl:justify-end"
          >
            {t("people.employees-salaries-page.table.actions.submit") ||
              "Submit"}
          </DropdownMenuItem>
        )}

        {/* Approve option for submitted status */}
        {hasApproveAction && (
          <DropdownMenuItem
            onClick={() => onApproveSalary!(row.original.id)}
            disabled={isApproving}
            className="cursor-pointer font-medium text-green-600 rtl:justify-end"
          >
            {t("people.employees-page.profile.salary.table.actions.approve")}
          </DropdownMenuItem>
        )}

        {/* Reject option for submitted status */}
        {hasRejectAction && (
          <DropdownMenuItem
            onClick={() => onRejectSalary!(row.original.id)}
            disabled={isRejecting}
            className="cursor-pointer font-medium text-red-600 rtl:justify-end"
          >
            {t("people.employees-page.profile.salary.table.actions.reject")}
          </DropdownMenuItem>
        )}

        {/* No actions available message */}
        {!hasActions && (
          <DropdownMenuItem
            className="cursor-default text-gray-400 rtl:justify-end"
            disabled
          >
            <span className="text-sm font-semibold text-end">
              {t("common.NoActionsAvailable")}
            </span>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
