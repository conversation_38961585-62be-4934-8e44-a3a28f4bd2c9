"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { TFunction } from "@/types";
import { ColumnDef, TableMeta } from "@tanstack/react-table";
import { TDevice } from "../../../type/devices/device";
import DeviceActions from "./device-actions";
import { formatDate } from "@/lib/dateFormatter";
import DeviceStatus from "@/components/status/device-status";
import { DEVICE_STATUS } from "@/constants/enum";
import { mapDeviceStatusToCanonical } from "@/constants/translations-mapping";
import { Locale } from "@/i18n/routing";
import Link from "next/link";
import Image from "next/image";

interface TableMetaWithTranslation extends TableMeta<TDevice> {
  t: TFunction;
  locale?: Locale;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getMeta = (table: any) => table.options.meta as TableMetaWithTranslation;

export const columns: ColumnDef<TDevice>[] = [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#ACBCBB] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    accessorKey: "name",
    id: "name",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div> {t("people.devices-page.table.columns.name")}</div>;
    },
    cell: ({ row }) => {
      const device = row.original;
      return (
        <p className="text-sm font-semibold text-black text-start">
          {device.attributes.name}
        </p>
      );
    },
  },
  {
    accessorKey: "ip_address",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("people.devices-page.table.columns.ip_address")}</div>;
    },
    cell: ({ row }) => {
      const device = row.original;
      return (
        <p className="text-sm font-semibold text-gray-500">
          {device.attributes.ip_address}
        </p>
      );
    },
  },
  {
    accessorKey: "adapter_type",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("people.devices-page.table.columns.adapter_type")}</div>;
    },
    cell: ({ row }) => {
      const device = row.original;
      return (
        <p className="text-sm font-semibold text-gray-500">
          {device.attributes.adapter_type}
        </p>
      );
    },
  },
  {
    accessorKey: "location",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("people.devices-page.table.columns.location")}</div>;
    },
    cell: ({ row }) => {
      const device = row.original;
      const location = device.attributes.location;
      // Convert location value to display text
      const locationDisplay =
        location === "athar_1"
          ? "Athar 1"
          : location === "athar_2"
            ? "Athar 2"
            : location || "-";
      return (
        <p className="text-sm font-semibold text-gray-500">{locationDisplay}</p>
      );
    },
  },
  {
    accessorKey: "port",
    enableColumnFilter: true,
    meta: {
      filterType: "number",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("people.devices-page.table.columns.port")}</div>;
    },
    cell: ({ row }) => {
      const device = row.original;
      return (
        <p className="text-sm font-semibold text-gray-500">
          {device.attributes.port}
        </p>
      );
    },
  },
  {
    accessorKey: "created_at",
    enableColumnFilter: true,
    meta: {
      filterType: "date",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("people.devices-page.table.columns.created_at")}</div>;
    },
    cell: ({ row, table }) => {
      const { locale } = getMeta(table);
      const device = row.original;
      const dateStr = device.attributes.created_at;
      const formattedDate = dateStr ? formatDate(dateStr, locale ?? "en") : "-";

      return (
        <p className="text-sm font-semibold text-gray-500">{formattedDate}</p>
      );
    },
  },
  {
    accessorKey: "updated_at",
    enableColumnFilter: true,
    meta: {
      filterType: "date",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("people.devices-page.table.columns.updated_at")}</div>;
    },
    cell: ({ row, table }) => {
      const { locale } = getMeta(table);
      const device = row.original;
      const dateStr = device.attributes.updated_at;
      const formattedDate = dateStr ? formatDate(dateStr, locale ?? "en") : "-";

      return (
        <p className="text-sm font-semibold text-gray-500">{formattedDate}</p>
      );
    },
  },
  {
    accessorKey: "last_seen_at",
    enableColumnFilter: true,
    meta: {
      filterType: "date",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("people.devices-page.table.columns.last_seen_at")}</div>;
    },
    cell: ({ row, table }) => {
      const { locale } = getMeta(table);
      const device = row.original;
      const dateStr = device.attributes.last_seen_at;
      const formattedDate = dateStr ? formatDate(dateStr, locale ?? "en") : "-";

      return (
        <p className="text-sm font-semibold text-gray-500">{formattedDate}</p>
      );
    },
  },
  {
    accessorKey: "status",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => (
      <div className="justify-start">
        {getMeta(table).t("people.devices-page.table.columns.status")}
      </div>
    ),
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const device = row.original;
      const status = device.attributes.status;
      const canonicalStatus = mapDeviceStatusToCanonical(status);

      // Get the translated label from the translation files
      const translatedLabel = t(
        `common.status.device-status.${canonicalStatus}`,
      );

      return (
        <div className="flex items-center text-start justify-start">
          <DeviceStatus
            status={canonicalStatus as DEVICE_STATUS}
            label={translatedLabel}
          />
        </div>
      );
    },
  },
  {
    id: "actions",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("people.devices-page.table.columns.actions")}</div>;
    },

    cell: ({ row, table }) => {
      const device = row.original;
      const { locale } = getMeta(table);
      return (
        <div className="flex items-center gap-2">
          <Link
            href={`/${locale}/people/devices/${device.id}`}
            className="p-2 !m-0 border rounded-lg hover:border-black/50 shrink-0"
          >
            <Image
              width={18}
              height={18}
              src={"/images/icons/eye.svg"}
              alt="eye icon"
            />
          </Link>
          <DeviceActions device={device} />
        </div>
      );
    },
  },
];
