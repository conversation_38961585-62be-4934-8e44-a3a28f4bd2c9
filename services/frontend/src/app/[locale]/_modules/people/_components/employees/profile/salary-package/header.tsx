"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import React, { useState } from "react";
import dynamic from "next/dynamic";
import Loader from "@/components/loader";
import { useEmployeeDetails } from "../../../../hooks/employees/useEmployeeDetails";
import { SalaryPackageHeaderSkeleton } from "../../../../skeletons/employee-profile-header-skeleton";
import SalaryForm from "./salary-form";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { TSalaryPackageData } from "../../../../type/salary-package";
import { mutate } from "swr";
import LoaderPortal from "@/components/loader/loader-portal";
import { SalaryHistoryChangesModal } from "./salary-history-changes-modal";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";
import { useCurrentEmployee } from "@/hooks/people/getCurrentEmployee";

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
  },
);

const CalculatePeriodModal = dynamic(
  () =>
    import("../calculate-period-modal").then((mod) => mod.CalculatePeriodModal),
  {
    ssr: false,
    loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
  },
);

type SalaryPackageHeaderProps = {
  title: string;
  employeeId?: string;
};

const SalaryPackageHeader = ({
  title,
  employeeId,
}: SalaryPackageHeaderProps) => {
  const t = useTranslations();
  const { hasPermission } = usePermission();
  const { profile: currentEmployee } = useCurrentEmployee();
  const [showSalaryForm, setShowSalaryForm] = useState(false);
  const [formMode, setFormMode] = useState<"create" | "update">("create");
  const [showCalculatePeriodModal, setShowCalculatePeriodModal] =
    useState(false);
  const [showSalaryHistoryChanges, setShowSalaryHistoryChanges] =
    useState(false);

  const {
    salaryPackage,
    mutate: mutateEmployeeDetails,
    isLoading,
  } = useEmployeeDetails(employeeId || "");

  const isUpdateMode = !!salaryPackage;
  const buttonText = isUpdateMode
    ? t("people.salary-package.update.button2")
    : t("people.salary-package.create.button");

  // Check if viewing own profile
  const isViewingOwnProfile = currentEmployee?.id === employeeId;

  // Permission checks based on backend role assignments:
  // - HR roles: create_others (for others only), manage_others, read
  // - Financial Manager: create (general), approve, read
  // - Employees: read_own only
  const canCreateSalaryPackage = isViewingOwnProfile
    ? // For own profile: only general create permission (Financial Manager)
      // create_others is specifically for others, NOT for self
      hasPermission(PermissionEnum.CREATE_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.MANAGE_SALARY_PACKAGE)
    : // For others' profiles: create_others (HR) OR general create (Financial) OR manage
      hasPermission(PermissionEnum.CREATE_OTHERS_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.CREATE_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.MANAGE_OTHERS_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.MANAGE_SALARY_PACKAGE);

  const canUpdateSalaryPackage = isViewingOwnProfile
    ? // For own profile: can update own or have general management permissions
      hasPermission(PermissionEnum.UPDATE_OWN_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.MANAGE_SALARY_PACKAGE)
    : // For others' profiles: need manage_others permission
      hasPermission(PermissionEnum.MANAGE_OTHERS_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.MANAGE_SALARY_PACKAGE);

  const canCalculateSalary = hasPermission(
    PermissionEnum.CALCULATE_SALARY_CALCULATION,
  );

  const canViewSalaryHistory = isViewingOwnProfile
    ? // For own profile: can read own or have general read permissions
      hasPermission(PermissionEnum.READ_OWN_SALARY_PACKAGE) ||
      hasPermission(PermissionEnum.READ_SALARY_PACKAGE)
    : // For others' profiles: need general read permission
      hasPermission(PermissionEnum.READ_SALARY_PACKAGE);

  // Show create/update button based on permissions
  const showSalaryPackageButton = isUpdateMode
    ? canUpdateSalaryPackage
    : canCreateSalaryPackage;

  const handleButtonClick = () => {
    if (isUpdateMode) {
      setFormMode("update");
    } else {
      setFormMode("create");
    }
    setShowSalaryForm(true);
  };

  const handleCalculatePeriod = () => {
    setShowCalculatePeriodModal(true);
  };

  const handleShowSalaryHistoryChanges = () => {
    setShowSalaryHistoryChanges(true);
  };

  const handleSalaryPackageCreated = () => {
    setShowSalaryForm(false);
    mutateEmployeeDetails(); // Refresh employee details
    // Refresh salary package history with the exact same cache key used by useSalaryPackageHistory
    if (employeeId) {
      mutate(`/api/employees/${employeeId}/salary_packages?sort=`);
    }
  };

  const handleSalaryPackageUpdated = (_updatedPackage: TSalaryPackageData) => {
    setShowSalaryForm(false);
    mutateEmployeeDetails(); // Refresh employee details
    // Refresh salary package history with the exact same cache key used by useSalaryPackageHistory
    if (employeeId) {
      mutate(`/api/employees/${employeeId}/salary_packages?sort=`);
    }
  };

  if (isLoading) {
    return <SalaryPackageHeaderSkeleton />;
  }

  return (
    <>
      <div className="flex items-center justify-between mb-4 flex-wrap gap-4">
        <h2>{title}</h2>
        {employeeId && (
          <div className="flex items-center gap-3">
            {showSalaryPackageButton && (
              <Button
                className="h-10 rounded-[9px] font-medium text-xs"
                onClick={handleButtonClick}
              >
                {buttonText}
              </Button>
            )}
            {canCalculateSalary && (
              <Button
                variant={"ghost"}
                className="h-10 rounded-[9px] font-medium text-xs bg-gray-100 border-none text-gray-800"
                onClick={handleCalculatePeriod}
                type="button"
              >
                {t("people.salary-calculation.calculate-period.button")}
              </Button>
            )}
            {canViewSalaryHistory && (
              <Button
                variant={"ghost"}
                className="h-10 rounded-[9px] font-medium text-xs bg-gray-100 border-none text-gray-800"
                onClick={handleShowSalaryHistoryChanges}
                type="button"
              >
                {t("people.salary-package.changes-log.button")}
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Unified Salary Form Modal - For both create and update */}
      {showSalaryForm && employeeId && (
        <ResponsiveDialog
          open={showSalaryForm}
          onOpenChange={setShowSalaryForm}
          className="h-[93vh]"
          header={
            <>
              <div className="px-6 py-[22px] border-b">
                <DialogTitle className="font-semibold text-[18px] leading-[28px]">
                  {formMode === "create"
                    ? t("people.salary-package.create.title")
                    : t("people.salary-package.update.title")}
                </DialogTitle>
                <DialogDescription className="text-sm text-gray-600 mt-1 sr-only">
                  {formMode === "create"
                    ? t("people.salary-package.create.description")
                    : t("people.salary-package.update.description")}
                </DialogDescription>
              </div>
            </>
          }
        >
          <SalaryForm
            employeeId={employeeId}
            onClose={() => setShowSalaryForm(false)}
            existingPackage={formMode === "update" ? salaryPackage : undefined}
            onSalaryPackageCreated={handleSalaryPackageCreated}
            onSalaryPackageUpdated={handleSalaryPackageUpdated}
            mode={formMode}
          />
        </ResponsiveDialog>
      )}

      {/* Calculate Period Modal */}
      {showCalculatePeriodModal && (
        <CalculatePeriodModal
          isOpen={showCalculatePeriodModal}
          onClose={() => setShowCalculatePeriodModal(false)}
          employeeId={employeeId}
        />
      )}

      {/* Salary History Changes Modal */}
      {showSalaryHistoryChanges && employeeId && (
        <SalaryHistoryChangesModal
          employeeId={employeeId}
          isOpen={showSalaryHistoryChanges}
          onClose={() => setShowSalaryHistoryChanges(false)}
        />
      )}
    </>
  );
};

export default SalaryPackageHeader;
