"use client";

import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { RowSelectionState } from "@tanstack/react-table";
import { employeeRolesProjectsColumns } from "./employee-roles-projects-columns";
import { useSearchParams } from "next/navigation";
import { useEmployeeRoleProjectMutations } from "../../../../hooks/employees/useEmployeeRoleProjectMutations";
import { TRoleProjectPair } from "../../../../type/employee-roles-projects";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import dynamic from "next/dynamic";
import LoaderPortal from "@/components/loader/loader-portal";
import { AddRoleProjectForm } from "./add-role-project-form";

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
  },
);

type EmployeeRolesProjectsTableProps = {
  employeeId: string;
  roleProjectPairs: TRoleProjectPair[];
  isLoading: boolean;
  error: string | null;
  mutateData: () => void;
};

export const EmployeeRolesProjectsTable = ({
  employeeId,
  roleProjectPairs,
  isLoading,
  error,
  mutateData,
}: EmployeeRolesProjectsTableProps) => {
  const t = useTranslations();
  const locale = useLocale();
  const searchParams = useSearchParams();
  const limit = searchParams.get("limit") ?? "5";
  const page = searchParams.get("page") ?? "1";
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  // Get mutations for employee roles and projects
  const { deleteRoleProject, isDeleting } = useEmployeeRoleProjectMutations({
    employeeId,
    onSuccess: () => {
      mutateData();
    },
  });
  const [createModalOpen, setIsCreateModalOpen] = useState(false);

  // Calculate pagination
  const totalCount = roleProjectPairs.length;
  const pageSize = Number(limit);
  const currentPage = Number(page);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalCount);

  // Slice the data for the current page
  const paginatedData = roleProjectPairs.slice(startIndex, endIndex);

  // Create role project button component
  const CreateRoleProjectButton = (
    <Button
      onClick={() => setIsCreateModalOpen(true)}
      className="sm:min-w-[169px] min-h-12 max-h-12 shadow-none font-semibold text-base flex gap-2 items-center"
    >
      <PlusCircle className="h-5 w-5" />
      {t("people.employees-page.profile.roles-projects.add-role-project")}
    </Button>
  );

  return (
    <>
      <DataTable
        data={paginatedData}
        dataCount={totalCount}
        columns={employeeRolesProjectsColumns}
        title={t("people.employees-page.profile.tabs.roles-projects")}
        meta={{
          t,
          locale: locale,
          onDeleteRoleProject: deleteRoleProject,
          isDeleting,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.employees-page.profile.roles-projects.table"
        isLoading={isLoading}
        headerActions={CreateRoleProjectButton}
        initialLimit={5}
        hideFilters={true}
        hideSearch={true}
        error={error ? new Error(error) : null}
      />

      {/* Pagination */}
      <div className="w-full pt-[18px]">
        <PaginationWithLinks
          page={Number(page)}
          pageSize={Number(limit)}
          totalCount={totalCount}
          firstLastCounts={{
            firstCount: startIndex + 1,
            lastCount: endIndex,
          }}
          isLoading={isLoading}
          pageSizeSelectOptions={{
            pageSizeOptions: [5, 10, 25, 30, 45, 50],
            pageSizeSearchParam: "limit",
          }}
        />
      </div>

      {/* Add Role Project Dialog */}
      {createModalOpen && (
        <ResponsiveDialog
          open={createModalOpen}
          onOpenChange={setIsCreateModalOpen}
          header={
            <>
              <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
                {t(
                  "people.employees-page.profile.roles-projects.add-dialog.title",
                )}
              </DialogTitle>
              <DialogDescription className="sr-only">
                {t(
                  "people.employees-page.profile.roles-projects.add-dialog.description",
                )}
              </DialogDescription>
            </>
          }
        >
          <AddRoleProjectForm
            employeeId={employeeId}
            onSuccess={() => {
              setIsCreateModalOpen(false);
              mutateData();
            }}
            onCancel={() => setIsCreateModalOpen(false)}
          />
        </ResponsiveDialog>
      )}
    </>
  );
};
