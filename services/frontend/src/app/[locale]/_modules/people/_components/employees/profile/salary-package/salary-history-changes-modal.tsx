"use client";

import React from "react";
import { useLocale, useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import FormattedCurrency from "@/components/formatted-currency";
import { useSalaryPackageHistory } from "../../../../hooks/employees/useSalaryPackageHistory";
import { formatDate } from "@/lib/dateFormatter";
import Loader from "@/components/loader";
import { SalaryHistoryChangesModalSkeleton } from "../../../../skeletons/salary-package-skeleton";

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
  },
);

type SalaryHistoryChangesModalProps = {
  employeeId: string;
  isOpen: boolean;
  onClose: () => void;
};

export const SalaryHistoryChangesModal = ({
  employeeId,
  isOpen,
  onClose,
}: SalaryHistoryChangesModalProps) => {
  const t = useTranslations();
  const locale = useLocale();

  const { salaryPackageHistory, isLoading } = useSalaryPackageHistory(
    1,
    100, // Get all history records
    "effective_date",
    employeeId,
  );

  const renderSalaryChangeEntry = (entry: {
    id: string;
    attributes: {
      base_salary: string;
      housing_allowance: string;
      transportation_allowance: string;
      other_allowances: string;
      total_package_value: string;
      effective_date: string;
    };
  }) => {
    const {
      base_salary,
      housing_allowance,
      transportation_allowance,
      other_allowances,
      total_package_value,
      effective_date,
    } = entry.attributes;

    return (
      <div
        key={entry.id}
        className="border-b border-gray-200 pb-6 mb-6 last:border-b-0 last:pb-0 last:mb-0"
      >
        {/* Date and Total */}
        <div className="flex justify-between items-start mb-4">
          <div className="text-start">
            <p className="text-xs leading-5 text-gray-400 mb-1">
              {t("people.salary-package.changes-log.total-salary")}:
            </p>
            <FormattedCurrency
              amount={+total_package_value}
              numberStyle="text-xl leading-5 font-bold text-black"
              currencyStyle="hidden"
            />
          </div>
          <div className="flex items-baseline gap-2">
            <p className="text-xs text-gray-400 leading-5 mb-1">
              {t("people.salary-package.effective-period")}:
            </p>
            <p className="text-sm font-semibold text-gray-600">
              {formatDate(effective_date, locale)}
            </p>
          </div>
        </div>

        {/* Salary Breakdown - 4 items in a horizontal row like the design */}
        <div className="flex justify-between items-center text-sm">
          <div className="flex items-center gap-2 wrap flex-wrap">
            <span className="text-gray-400 text-xs font-normal leading-5">
              {t("common.form.salary.base_salary.label")}:
            </span>
            <FormattedCurrency
              amount={+base_salary}
              numberStyle="font-semibold text-sm leading-5 text-gray-600"
              currencyStyle="hidden"
            />
          </div>
          <div className="flex items-center gap-2 wrap flex-wrap">
            <span className="text-gray-400 text-xs font-normal leading-5">
              {t("common.form.salary.housing_allowance.label")}:
            </span>
            <FormattedCurrency
              amount={+housing_allowance}
              numberStyle="font-semibold text-sm leading-5 text-gray-600"
              currencyStyle="hidden"
            />
          </div>
          <div className="flex items-center gap-2 wrap flex-wrap">
            <span className="text-gray-400 text-xs font-normal leading-5">
              {t("common.form.salary.transportation_allowance.label")}:
            </span>
            <FormattedCurrency
              amount={+transportation_allowance}
              numberStyle="font-semibold text-sm leading-5 text-gray-600"
              currencyStyle="hidden"
            />
          </div>
          <div className="flex items-center gap-2 wrap flex-wrap">
            <span className="text-gray-400 text-xs font-normal leading-5">
              {t("common.form.salary.other_allowances.label")}:
            </span>
            <FormattedCurrency
              amount={+other_allowances}
              numberStyle="font-semibold text-sm leading-5 text-gray-600"
              currencyStyle="hidden"
            />
          </div>
        </div>
      </div>
    );
  };

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      className="h-[93vh] sm:!min-w-[600px]"
      header={
        <>
          <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
            {t("people.salary-package.changes-log.title")}
          </DialogTitle>
          <DialogDescription className="sr-only">
            {t("people.salary-package.changes-log.title")}
          </DialogDescription>
        </>
      }
    >
      <div className="space-y-6">
        {isLoading ? (
          <SalaryHistoryChangesModalSkeleton />
        ) : salaryPackageHistory.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {t("people.salary-package.changes-log.no-changes")}
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {salaryPackageHistory.map(renderSalaryChangeEntry)}
          </div>
        )}
      </div>
    </ResponsiveDialog>
  );
};
