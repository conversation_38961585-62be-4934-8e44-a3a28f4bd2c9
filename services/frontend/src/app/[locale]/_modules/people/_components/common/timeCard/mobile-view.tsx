import React from "react";
import { Tag } from "@/components/ui/tag";
import { AttendancePeriod } from "@/types/employee/daySummary";
import {
  formatTimeDisplay,
  getDefaultPeriod,
  getPeriodInfo,
} from "../../../utils/time-card-utils";
import { TFunction } from "@/types";

type MobileViewProps = {
  date?: string;
  startTime?: string;
  endTime?: string;
  total_work_minutes?: number;
  periods?: AttendancePeriod[];
  t: TFunction;
  locale: string;
};

export const MobileView: React.FC<MobileViewProps> = ({
  date,
  startTime,
  endTime,
  total_work_minutes,
  periods,
  t,
  locale,
}) => {
  return (
    <div className="flex flex-col md:hidden">
      {/* Check-in section */}
      <div className="flex justify-between items-center border-b pb-3 mb-3">
        <div>
          <div className="text-xs font-alex font-normal text-gray-500 mb-1">
            {t("people.employees-page.profile.attendance.timeCard.checkIn")}
          </div>
          <div className="text-sm font-medium font-alex">
            {formatTimeDisplay(date, startTime, locale)}
          </div>
        </div>
        <div className="text-sm font-medium font-alex text-gray-500">
          {startTime ? "صباحاً" : ""}
        </div>
      </div>

      {/* Time periods for mobile */}
      <div className="space-y-2 mb-3">
        {periods && periods.length > 0 ? (
          periods?.map((period, idx) => {
            const periodInfo = getPeriodInfo(period);
            const tooltipText = t(`${periodInfo.translationKey}`);
            const startTime = period.attributes.formatted_start_time;

            // Calculate time for display on the right
            const timeDisplay = startTime
              ? startTime.split(":")[0] + ":00"
              : "";
            const timeOfDay =
              parseInt(startTime?.split(":")[0] || "0") >= 12 ? "م" : "ص";

            return (
              <div key={idx} className="flex items-start justify-between">
                <div className="text-xs font-alex text-gray-500 me-2 text-start w-16">
                  {timeDisplay && `${timeDisplay} ${timeOfDay}`}
                </div>
                <div className="flex-grow">
                  <Tag
                    className="w-full text-sm py-2 text-center text-black !rounded-lg font-alex font-medium"
                    bordered={true}
                    style={{
                      background: periodInfo.background,
                      borderColor: periodInfo.border,
                      borderWidth: "1.5px",
                    }}
                  >
                    {tooltipText}
                  </Tag>
                </div>
              </div>
            );
          })
        ) : (
          <div className="flex items-center justify-between">
            <div className="flex-grow">
              <Tag
                className="w-full text-sm py-2 text-center text-black !rounded-lg font-alex font-medium"
                bordered
                style={{
                  background: getPeriodInfo(getDefaultPeriod()).background,
                  borderColor: getPeriodInfo(getDefaultPeriod()).border,
                }}
              >
                {t(`${getPeriodInfo(getDefaultPeriod()).translationKey}`)}
              </Tag>
            </div>
            <div className="text-xs font-alex text-gray-500 mr-2 text-left w-16">
              09:00 م
            </div>
          </div>
        )}
      </div>

      {/* Check-out section */}
      <div className="flex justify-between items-center border-b pb-3 mb-3">
        <div>
          <div className="text-xs font-alex font-normal text-gray-500 mb-1">
            {t("people.employees-page.profile.attendance.timeCard.checkOut")}
          </div>
          <div className="text-sm font-medium font-alex">
            {formatTimeDisplay(date, endTime, locale)}
          </div>
        </div>
        <div className="text-sm font-medium font-alex text-gray-500">
          {endTime ? "مساءً" : ""}
        </div>
      </div>

      {/* Work duration section */}
      {(total_work_minutes ?? 0) >= 0 && (
        <div className="flex justify-between items-center">
          <div className="text-xs font-alex font-normal text-gray-500">
            {t("people.employees-page.profile.attendance.timeCard.period")}
          </div>
          <div className="text-sm font-medium font-alex">
            {total_work_minutes && total_work_minutes > 0
              ? (total_work_minutes / 60).toFixed(1)
              : 0}
            {t("people.employees-page.profile.attendance.timeCard.hour")}
          </div>
        </div>
      )}
    </div>
  );
};
