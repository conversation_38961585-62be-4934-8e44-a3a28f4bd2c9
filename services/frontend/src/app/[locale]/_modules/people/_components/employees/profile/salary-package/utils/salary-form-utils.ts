import { format } from "date-fns";
import { formatNumber } from "@/lib/format-number";
import { Locale } from "@/i18n/routing";
import { TSalaryPackageData } from "@/app/[locale]/_modules/people/type/salary-package";
import { SalaryPackageSchemaType } from "@/app/[locale]/_modules/people/schemas/salaryPackageSchema";
import { LANGUAGES } from "@/constants/enum";

/**
 * Normalize values for comparison (removes commas, trims strings, formats dates)
 */
export const normalizeValue = (val: unknown): string => {
  if (typeof val === "string") {
    return val.replace(/,/g, "").trim();
  }
  if (val instanceof Date) {
    return format(val, "yyyy-MM-dd");
  }
  return String(val || "");
};

/**
 * Calculate total package value from form values
 */
export const calculateTotalPackage = (
  values: Partial<SalaryPackageSchemaType>,
): number => {
  const parseAmount = (val?: string) =>
    parseFloat(val?.replace(/,/g, "") || "0") || 0;

  return (
    parseAmount(values.base_salary) +
    parseAmount(values.housing_allowance) +
    parseAmount(values.transportation_allowance) +
    parseAmount(values.other_allowances)
  );
};

/**
 * Get default form values based on mode and existing package
 */
export const getDefaultFormValues = (
  mode: "create" | "update",
  employeeId: string,
  existingPackage?: TSalaryPackageData | null,
  locale?: Locale,
): SalaryPackageSchemaType => {
  if (mode === "create") {
    return {
      employee_id: employeeId,
      base_salary: "",
      housing_allowance: "",
      transportation_allowance: "",
      other_allowances: "",
      effective_date: new Date(),
      notes: "",
    };
  }

  const packageAttrs = existingPackage?.attributes;
  const effectiveDate = packageAttrs?.effective_date
    ? new Date(packageAttrs.effective_date)
    : new Date();

  return {
    employee_id: employeeId,
    base_salary: formatNumber(
      Number(packageAttrs?.base_salary || 0),
      locale || LANGUAGES.ENGLISH,
    ),
    housing_allowance: formatNumber(
      Number(packageAttrs?.housing_allowance || 0),
      locale || LANGUAGES.ENGLISH,
    ),
    transportation_allowance: formatNumber(
      Number(packageAttrs?.transportation_allowance || 0),
      locale || LANGUAGES.ENGLISH,
    ),
    other_allowances: formatNumber(
      Number(packageAttrs?.other_allowances || 0),
      locale || LANGUAGES.ENGLISH,
    ),
    effective_date: effectiveDate,
    notes: packageAttrs?.notes || "",
  };
};

/**
 * Prepare form data for submission
 */
export const prepareFormDataForSubmission = (
  data: SalaryPackageSchemaType,
  mode: "create" | "update",
  existingPackageId?: string,
): FormData => {
  const formData = new FormData();

  // Only add ID for update mode
  if (mode === "update" && existingPackageId) {
    formData.append("id", existingPackageId);
  }

  Object.entries(data).forEach(([key, value]) => {
    if (value === null || value === undefined) return;

    if (key === "effective_date" && value instanceof Date) {
      formData.append(key, format(value, "dd-MM-yyyy"));
    } else if (
      [
        "base_salary",
        "housing_allowance",
        "transportation_allowance",
        "other_allowances",
      ].includes(key) &&
      typeof value === "string"
    ) {
      formData.append(key, value.replace(/,/g, ""));
    } else {
      formData.append(key, String(value));
    }
  });

  return formData;
};
