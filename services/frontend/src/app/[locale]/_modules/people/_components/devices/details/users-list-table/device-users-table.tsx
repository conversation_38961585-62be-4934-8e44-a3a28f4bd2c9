"use client";

import React, { useState } from "react";
import { DataTable } from "@/components/table";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { RowSelectionState } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { usersColumns } from "./device-users-columns";
import { useDeviceUsersList } from "../../../../hooks/devices/usersList/useDeviceUsersList";
import { TFunction } from "@/types";
import { TDeviceUser } from "../../../../type/devices/usersList";
import { updateDeviceUserCodes } from "../../../../actions/update-device-user-codes";
import { useToast } from "@/hooks/use-toast";
import dynamic from "next/dynamic";
import LoaderPortal from "@/components/loader/loader-portal";

const FixCodeDialog = dynamic(() => import("./fix-code-dialog"), {
  ssr: false,
  loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
});

type DeviceUsersTableProps = {
  showPagination?: boolean;
  searchParams?: {
    page: string;
    limit: string;
  };
  deviceId: string;
};

const DeviceUsersTable = ({
  showPagination = true,
  searchParams = { page: "1", limit: "5" },
  deviceId,
}: DeviceUsersTableProps) => {
  const t = useTranslations() as TFunction;
  const locale = useLocale();
  const { toast } = useToast();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [isFixCodeDialogOpen, setIsFixCodeDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<TDeviceUser | null>(null);
  const [isUpdatingCode, setIsUpdatingCode] = useState(false);

  const limit = parseInt(searchParams.limit ?? 5, 10);
  const page = parseInt(searchParams.page ?? 1, 10);

  const {
    data: users,
    employeeData,
    pagination,
    totalCount,
    error,
    isLoading,
    mutate,
  } = useDeviceUsersList(deviceId, page, limit);

  const handleFixCode = (user: TDeviceUser) => {
    setSelectedUser(user);
    setIsFixCodeDialogOpen(true);
  };

  const handleFixCodeSubmit = async (data: {
    systemCode: string;
    deviceCode: string;
  }) => {
    if (!selectedUser) return;

    // Extract mapping ID from user relationships
    const mappingId = selectedUser.relationships?.mapping?.data?.id;
    const isNewMapping = !mappingId; // If no mapping ID, this is a new mapping

    setIsUpdatingCode(true);
    try {
      const result = await updateDeviceUserCodes({
        deviceId,
        mappingId, // Will be undefined for new mappings
        systemCode: data.systemCode,
        deviceCode: data.deviceCode,
        isNewMapping, // Flag to indicate if this is a new mapping
      });

      if (result.success) {
        toast({
          title: t("common.toast.success"),
          description: result.success,
          variant: "default",
        });

        // Refresh the data
        mutate();

        setIsFixCodeDialogOpen(false);
        setSelectedUser(null);
      } else {
        toast({
          title: t("common.toast.error"),
          description: result.error,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating codes:", error);
      toast({
        title: t("common.toast.error"),
        description: t("common.error.generic"),
        variant: "destructive",
      });
    } finally {
      setIsUpdatingCode(false);
    }
  };

  return (
    <div>
      <DataTable
        data={users}
        dataCount={totalCount}
        columns={usersColumns}
        tableContainerClass="min-h-[240px] text-center"
        title={t("people.devices-page.users-table.title")}
        meta={{
          t,
          locale,
          employeeData,
          onFixCode: handleFixCode,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.devices-page.users-table"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
        tableHeadStyle="text-center"
        tableCellStyle="text-center"
      />
      <div className="w-full pt-[18px]">
        {showPagination && (
          <PaginationWithLinks
            page={pagination?.page ?? Number(page)}
            pageSize={Number(limit)}
            totalCount={Number(totalCount || 0)}
            firstLastCounts={{
              firstCount:
                ((pagination?.page ?? 1) - 1) * (pagination?.limit ?? 25) + 1,
              lastCount: Math.min(
                (pagination?.page ?? 1) * (pagination?.limit ?? 25),
                totalCount,
              ),
            }}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
            isLoading={isLoading}
            isDisabled={!users?.length}
          />
        )}
      </div>

      {isFixCodeDialogOpen && (
        <FixCodeDialog
          isOpen={isFixCodeDialogOpen}
          onClose={() => {
            setIsFixCodeDialogOpen(false);
            setSelectedUser(null);
          }}
          user={selectedUser}
          onSubmit={handleFixCodeSubmit}
          isLoading={isUpdatingCode}
          includedData={employeeData}
        />
      )}
    </div>
  );
};

export default DeviceUsersTable;
