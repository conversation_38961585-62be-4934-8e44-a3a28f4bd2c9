"use client";

import React from "react";
import { useTranslations, useLocale } from "next-intl";
import { usePathname, useRouter } from "next/navigation";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Locale } from "@/i18n/routing";

type EmployeeProfileTabsProps = {
  employeeId: string;
  locale: string;
};

type TabKey =
  | "attendance"
  | "attachments"
  | "leaves"
  | "roles-projects"
  | "salary-package";

export const EmployeeProfileTabs: React.FC<EmployeeProfileTabsProps> = ({
  employeeId,
  locale,
}) => {
  const t = useTranslations();
  const currentLocale: Locale = useLocale() as Locale;
  const isAr = currentLocale === "ar";
  const pathname = usePathname();
  const router = useRouter();

  const tabs: TabKey[] = [
    "attendance",
    "leaves",
    "attachments",
    "roles-projects",
    "salary-package",
  ];

  const activeTab =
    tabs.find((tab) => tab !== "attendance" && pathname.endsWith(`/${tab}`)) ||
    "attendance";

  const getTabRoute = (tab: TabKey) =>
    tab === "attendance"
      ? `/${locale}/people/employees/${employeeId}`
      : `/${locale}/people/employees/${employeeId}/${tab}`;

  const tabTriggerClass =
    "flex-1 text-base font-medium data-[state=active]:bg-transparent data-[state=active]:text-secondary data-[state=active]:shadow-none data-[state=active]:border-b-4 border-secondary rounded-none text-gray-400 pb-[14px] data-[state=active]:font-bold max-w-28 max-lg:px-0.5 ms-2";

  return (
    <div className="bg-none border-b border-gray-200 !mt-4 sm:!mt-8">
      <Tabs
        defaultValue="attendance"
        className="overflow-auto overflow-y-hidden custom-scroll"
        value={activeTab}
        dir={isAr ? "rtl" : "ltr"}
        onValueChange={(value) => {
          router.push(getTabRoute(value as TabKey));
        }}
      >
        <TabsList className="flex justify-start gap-10 py-[22px] grid-cols-5 bg-transparent max-w-full w-full">
          {tabs.map((tab) => (
            <TabsTrigger key={tab} value={tab} className={tabTriggerClass}>
              {t(`people.employees-page.profile.tabs.${tab}`)}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
};
