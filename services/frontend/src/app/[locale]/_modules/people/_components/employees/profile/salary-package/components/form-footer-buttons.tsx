import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LoaderCircle } from "lucide-react";
import { TFunction } from "@/types";

type FormFooterButtonsProps = {
  isPending: boolean;
  isDirty: boolean;
  onClose: () => void;
  t: TFunction;
};

export const FormFooterButtons: React.FC<FormFooterButtonsProps> = ({
  isPending,
  isDirty,
  onClose,
  t,
}) => {
  return (
    <div className="sticky border-t border-t-slate-200 pt-2.5 bottom-0 left-0 w-full rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6">
      <Button
        disabled={isPending || !isDirty}
        type="submit"
        className="w-full h-12 max-h-12"
      >
        {!isPending ? (
          t("common.buttonText.save")
        ) : (
          <LoaderCircle className="animate-spin text-white" />
        )}
      </Button>
      <Button
        type="button"
        variant="outline"
        className="w-full h-12 max-h-12"
        disabled={isPending}
        onClick={onClose}
      >
        {t("common.buttonText.cancel2")}
      </Button>
    </div>
  );
};
