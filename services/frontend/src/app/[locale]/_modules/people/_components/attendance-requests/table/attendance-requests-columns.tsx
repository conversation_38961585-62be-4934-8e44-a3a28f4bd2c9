"use client";
import { ColumnDef, Table, TableMeta } from "@tanstack/react-table";
import { Attendance_Event_type } from "@/constants/enum";
import { Locale } from "@/i18n/routing";
import {
  AttendanceEvent,
  TIncludedEmployee,
} from "../../../type/employee-leaves";
import { TFunction } from "@/types";
import { findEmployeeById } from "../../../utils/find-employee";
import CheckinStatus from "@/components/status/checkin-status";
import { mapCheckinStatusToCanonical } from "@/constants/translations-mapping";
import { formatDate } from "@/lib/dateFormatter";

// Define the translation function type
interface TableMetaWithTranslation extends TableMeta<AttendanceEvent> {
  t: TFunction;
  locale?: Locale;
  employeeData: TIncludedEmployee[];
}

// Helper function to get meta data
const getMeta = (table: Table<AttendanceEvent>) =>
  table.options.meta as TableMetaWithTranslation;

export const columns: ColumnDef<AttendanceEvent>[] = [
  // employee Name
  {
    accessorKey: "employeeName",

    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t(
            "people.attendance-requests-page.table.columns.employeeName",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { employeeData } = getMeta(table);
      const employeeId = row.original.relationships.employee.data.id;
      const employee = findEmployeeById(employeeData, employeeId);
      return (
        employee && (
          <p className="text-sm font-semibold text-start text-gray-500">
            {employee.name}
          </p>
        )
      );
    },
    meta: { filterVariant: "select" },
  },

  // activity Type
  {
    accessorKey: "activity_type",
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t(
            "people.attendance-requests-page.table.columns.activity_type",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const type = row.original.attributes.activity_type || "regular";
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {t(
            `people.employees-page.profile.attendance.timeCard.periodKeys.${type}`,
          )}
        </p>
      );
    },
  },

  // request date
  {
    accessorKey: "requestDate",
    header: ({ table }) => {
      return (
        <div>
          {getMeta(table).t(
            "people.attendance-requests-page.table.columns.requestDate",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { locale, t } = getMeta(table);
      const dateStr = row.original.attributes.timestamp;
      const stringFormated = formatDate(dateStr, locale ?? "ar", "dd-MM hh:mm");

      return (
        <p className="text-sm font-semibold text-gray-500">{stringFormated}</p>
      );
    },
  },
  {
    accessorKey: "status",
    header: ({ table }) =>
      getMeta(table).t("people.attendance-requests-page.table.columns.status"),
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const status = row.original.attributes
        .event_type as Attendance_Event_type;
      const final = mapCheckinStatusToCanonical(status);
      return (
        <div className="mx-auto flex justify-center">
          <CheckinStatus
            status={final}
            label={t(
              `people.attendance-requests-page.table.status.${status.toLowerCase()}`,
            )}
          />
        </div>
      );
    },
  },
];
