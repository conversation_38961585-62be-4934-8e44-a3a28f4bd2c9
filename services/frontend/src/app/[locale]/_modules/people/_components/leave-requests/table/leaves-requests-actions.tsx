import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LANGUAGES } from "@/constants/enum";
import { Row } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { CiMenuKebab } from "react-icons/ci";
import {
  Eye,
  NoteAdd,
  RightCircle,
} from "../../../../../../../../public/images/icons";
import { X } from "lucide-react";
import { Locale } from "@/i18n/routing";
import { LeaveDetail, TIncludedEmployee } from "../../../type/employee-leaves";
import { TEmployee } from "../../../type/employee";
import { findEmployeeById } from "../../../utils/find-employee";

type LeavesRequestsActionsProps<TData> = {
  row: Row<TData>;
  onShowDetails: (value: <PERSON><PERSON>, employee: TEmployee) => void;
  onAccept: (value: <PERSON><PERSON>, employee: TEmployee) => void;
  onReject: (value: TData, employee: TEmployee) => void;
  employeeData: TIncludedEmployee[];
  // onShowNote: (value: TData, employee: TEmployee) => void;
};

const LeavesRequestsActions = <TData extends LeaveDetail>({
  row,
  onShowDetails,
  employeeData,
  // onShowNote,
  onAccept,
  onReject,
}: LeavesRequestsActionsProps<TData>) => {
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();
  const employeeId = row.original.relationships.employee.data.id;
  const employee = findEmployeeById(employeeData, employeeId);
  if (!employee) {
    return null;
  }
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 w-8 p-0 border border-[#EEEEEE] rounded-lg"
        >
          <span className="sr-only">Open menu</span>
          <CiMenuKebab className="stroke-[0.5px]" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        lang={locale}
        className="w-60 max-w-60 rounded-2xl space-y-1 font-semibold text-sm text-start text-[#727A90] p-2"
        align={isAr ? "start" : "end"}
      >
        {/* View Details */}
        <DropdownMenuItem
          className="flex rtl:flex-row-reverse items-center min-h-10 group rounded-2xl"
          onClick={() => onShowDetails(row.original, employee)}
        >
          <span>
            <Eye className="!w-6 !h-6 stroke-[#6B7280] group-hover:stroke-secondary" />
          </span>
          <span>{t("cm.table.actions.Action1")}</span>
        </DropdownMenuItem>
        {/* Accept */}
        {employee.status === "waiting" && (
          <DropdownMenuItem
            className="flex rtl:flex-row-reverse items-center min-h-10 group rounded-2xl"
            onClick={() => onAccept(row.original, employee)}
          >
            <span>
              <RightCircle className="!w-6 !h-6 stroke-[#6B7280]" />
            </span>
            <span>{t("people.leaves-requests-component.actions.accept")}</span>
          </DropdownMenuItem>
        )}
        {/* write note */}
        {/* <DropdownMenuItem
          className="flex rtl:flex-row-reverse items-center min-h-10 group rounded-2xl"
          onClick={() => onShowNote(row.original, employee)}
        >
          <span>
            <NoteAdd className="!w-6 !h-6 stroke-[#6B7280] group-hover:stroke-red-500" />
          </span>
          <span>{t("people.leaves-requests-component.actions.writeNote")}</span>
        </DropdownMenuItem> */}
        {/* Reject */}
        {employee.status === "waiting" && (
          <DropdownMenuItem
            className="flex border-t border-gray-100 p-2.5 pb-4 rtl:flex-row-reverse items-center min-h-10 group rounded-2xl text-sm font-semibold"
            onClick={() => onReject(row.original, employee)}
          >
            <span>
              <X className="h-4 w-4" />
            </span>
            <span>{t("people.leaves-requests-component.actions.reject")}</span>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export { LeavesRequestsActions };
