import { Skeleton } from "@/components/ui/skeleton";

export const EmployeeProfileHeaderSkeleton = () => {
  return (
    <div className="bg-white rounded-[20px] max-lg:px-3 p-6 border border-gray-200">
      <div className="flex flex-col lg:flex-row justify-between lg:gap-3 items-center">
        <div className="w-full flex flex-col lg:flex-row gap-6 justify-between lg:max-w-[622px] items-center">
          {/* Profile Image Skeleton */}
          <div className="flex flex-col lg:flex-row gap-3 items-center">
            <div className="relative rounded-full">
              <Skeleton className="w-[72px] h-[72px] rounded-full bg-gray-200" />
            </div>

            <div className="flex flex-col gap-1 flex-1 xl:min-w-[120px]">
              <div className="text-center lg:text-start">
                <Skeleton className="h-[30px] w-40 mb-2 bg-gray-200" />
              </div>
            </div>
          </div>

          <span className="h-14 text-gray-400 bg-gray-100 w-0.5 hidden lg:block"></span>

          {/* Contact Information Skeleton */}
          <div className="flex max-xl:flex-wrap gap-6 sm:gap-2 text-center sm:text-start justify-between sm:justify-around lg:justify-start lg:gap-6 items-center w-full text-sm">
            <div className="flex flex-col gap-1">
              <Skeleton className="h-5 w-16 mb-1 bg-gray-200" />
              <Skeleton className="h-5 w-32 bg-gray-200" />
            </div>
            <div className="flex flex-col gap-1">
              <Skeleton className="h-5 w-16 mb-1 bg-gray-200" />
              <Skeleton className="h-5 w-32 bg-gray-200" />
            </div>
            <div className="flex flex-col gap-1">
              <div className="flex flex-col gap-1 text-start">
                <Skeleton className="h-5 w-24 mb-1 bg-gray-200" />
                <div className="flex items-center gap-1">
                  <Skeleton className="h-5 w-36 bg-gray-200" />
                  <Skeleton className="h-5 w-8 bg-gray-200 rounded-lg" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Send Email Button Skeleton */}
        <div className="mt-6 lg:mt-0 w-full lg:w-auto flex flex-col items-center gap-3">
          <div className="w-full lg:w-auto max-sm:max-w-[295px]">
            <div className="w-full sm:w-11/12 mx-auto lg:w-auto flex flex-col gap-3">
              <Skeleton className="w-full h-14 lg:w-[260px] bg-gray-200 rounded-md" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Salary Package Header Skeleton
export const SalaryPackageHeaderSkeleton = () => {
  return (
    <div className="flex items-center justify-between mb-4">
      <Skeleton className="h-8 w-48 bg-gray-200" />
      <div className="flex items-center gap-3">
        <Skeleton className="h-10 w-32 bg-gray-200 rounded-[9px]" />
        <Skeleton className="h-10 w-32 bg-gray-200 rounded-[9px]" />
      </div>
    </div>
  );
};
