export type TSalaryPackageAttributes = {
  base_salary: string;
  housing_allowance: string;
  transportation_allowance: string;
  other_allowances: string;
  effective_date: string;
  end_date: string | null;
  status: "active" | "inactive";
  active?: boolean;
  adjustment_reason: string | null;
  previous_package_id: string | null;
  notes: string;
  created_at: string;
  updated_at: string;
  total_package_value: string;
};

export type TSalaryPackageRelationships = {
  employee: {
    data: {
      id: string;
      type: string;
    };
  };
};

export type TSalaryPackageData = {
  id: string;
  type: string;
  attributes: TSalaryPackageAttributes;
  relationships: TSalaryPackageRelationships;
};

export type TSalaryPackageResponse = {
  data: TSalaryPackageData[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

export type TSalaryPackageFormData = {
  employee_id: string;
  base_salary: string;
  housing_allowance: string;
  transportation_allowance: string;
  other_allowances: string;
  effective_date: Date;
  notes: string;
};
