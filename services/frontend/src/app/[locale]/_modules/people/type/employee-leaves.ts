import { TEmployee, TEmployeeData } from "./employee";

// Define the type for leave details attributes
export type LeaveDetailAttributes = {
  leave_type: string;
  leave_duration: string; // "full_day", "half_day_morning", or "half_day_afternoon"
  status: string;
  start_date: string;
  end_date: string;
  reason: string;
  created_at?: string;
  updated_at?: string;
  duration?: number;
  working_days?: number;
  with_deduction?: boolean;
  attachments?: string[];
};

// Define the type for leave details
export type LeaveDetail = {
  id: string;
  type: string;
  attributes: LeaveDetailAttributes;
  relationships: {
    employee: {
      data: {
        id: string;
        type: string;
      };
    };
    approval_request: {
      data: {
        id: string;
        type: string;
      };
    };
  };
};

export type TIncludedEmployee = {
  id: string;
  type: string;
  attributes: TEmployee;
};

// Define the API response type for leaves
export type EmployeeLeavesResponse = {
  data: LeaveDetail[];
  included?: TIncludedEmployee[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

// Define the leave statistics type
export type EmployeeLeavesStats = {
  pendingLeaves: {
    value: number;
    percentageChange: number;
  };
  rejectedLeaves: {
    value: number;
    percentageChange: number;
  };
  approvedLeaves: {
    value: number;
    percentageChange: number;
  };
  leaveRequests: {
    value: number;
    percentageChange: number;
  };
};
export type AttendanceEventResponse = {
  data: AttendanceEvent[];
  included: TEmployeeData[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

export type AttendanceEvent = {
  id: string;
  type: string;
  attributes: {
    timestamp: number;
    event_type: string;
    activity_type: string;
    location: string | null;
    notes: string | null;
    created_at: string;
    updated_at: string;
    inferred_type: string | null;
  };
  relationships: {
    employee: {
      data: {
        id: string;
        type: string;
      };
    };
  };
};
