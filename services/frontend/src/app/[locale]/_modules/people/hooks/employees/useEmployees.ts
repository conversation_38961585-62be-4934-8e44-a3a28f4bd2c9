import useSWR from "swr";
import qs from "qs";

import { fetcher } from "@/services/fetcher";
import { TEmployeesResponse } from "../../type/employee";
import { useSearchParams } from "next/navigation";
import { useFilterParams } from "@/hooks/filters";

export const useEmployees = (
  page: number,
  limit: number,
  sortBy: string = "-start_date",
) => {
  const searchParams = useSearchParams();
  const { filters } = useFilterParams("employees");
  const searchQuery = searchParams.get("search") || "";

  // When searching, we still want to use pagination
  const effectivePage = page;
  const effectiveLimit = limit;
  // Build the API URL with all necessary parameters
  const buildApiUrl = () => {
    const allParams = {
      sort: sortBy,
      page: effectivePage,
      limit: effectiveLimit,
      ...(searchQuery && { search: searchQuery }),
      ...(Object.keys(filters).length > 0 && { filter: filters })
    };

    const queryString = qs.stringify(allParams, {
      arrayFormat: 'repeat',
      encode: false
    });

    return `/api/employees?${queryString}`;
  };
  
  const apiUrl = buildApiUrl();

  const { data, error, isLoading, mutate } = useSWR<TEmployeesResponse>(
    apiUrl,
    fetcher,
  );

  const from = data?.meta?.pagination?.from;
  const to = data?.meta?.pagination?.to;

  return {
    employees: data?.data ?? [],
    totalCount: data?.meta?.pagination?.count ?? 0,
    isLoading,
    error,
    mutate,
    pagination: {
      firstResult: from,
      lastResult: to,
      limit: Number(limit),
      page: Number(page),
    },
  };
};
