"use client";

import { useOptimisticMutation } from "@/hooks/ues-optimistic-mutatation";
import { useTranslations } from "next-intl";
import { useState } from "react";

import { useToastMessage } from "@/hooks/use-toast-message";
import { deleteDevice as deleteDeviceAction } from "../../actions/device-action";
import { fetcher } from "@/services/fetcher";
import { TDeviceResponse, TDevice } from "../../type/devices/device";
import { mutate } from "swr";
import { useFilterParams } from "@/hooks/filters";
import { useApiUrl } from "@/hooks/useApiUrl";

type UseDeviceMutationsProps = {
  page?: string | number;
  limit?: string | number;
  search?: string;
  sort?: string;
  onSuccess?: () => void;
};

export const useDeviceMutations = ({
  page = 1,
  limit = 5,
  search = "",
  sort = "-id",
  onSuccess,
}: UseDeviceMutationsProps = {}) => {
  const { showToast } = useToastMessage();
  const t = useTranslations();
  const [isDeleting, setIsDeleting] = useState(false);
  const { filters } = useFilterParams("devices");

  const apiUrl = useApiUrl({
    baseUrl: "/api/attendance/devices",
    page: Number(page),
    limit: Number(limit),
    sort: String(sort),
    search: search || undefined,
    filters,
    tablePrefix: "devices",
  });

  const { data, error, isLoading, mutateData } = useOptimisticMutation<
    TDeviceResponse,
    { deviceId?: string; deviceData?: TDevice; mode?: "create" | "update" }
  >({
    key: apiUrl, // This will change when URL parameters change, causing SWR to refetch
    fetcher,
    mutations: {
      deleteDevice: {
        updateFn: (currentData, { deviceId }) => {
          if (!currentData || !deviceId) return currentData;

          return {
            ...currentData,
            data: currentData.data.filter((device) => device.id !== deviceId),
            meta: {
              ...currentData.meta,
              pagination: {
                ...currentData.meta.pagination,
                count: currentData.meta.pagination.count - 1,
              },
            },
          };
        },
        mutationFn: async ({ deviceId }) => {
          if (!deviceId) throw new Error("Device ID is required");

          setIsDeleting(true);
          try {
            const result = await deleteDeviceAction(deviceId);

            if (result.error) {
              throw new Error(result.error);
            }

            showToast(
              "success",
              t("people.devices-page.delete-dialog.success-message"),
            );

            if (onSuccess) {
              onSuccess();
            }

            // Return undefined to keep the optimistic update
            return { data: undefined };
          } catch (error) {
            showToast("error", t("common.error.generic"));
            throw error;
          } finally {
            setIsDeleting(false);
          }
        },
      },
    },
    swrOptions: {
      dedupingInterval: 2000,
      revalidateAfterMutation: false,
      keepPreviousData: true,
    },
  });

  const deleteDevice = async (deviceId: string) => {
    try {
      await mutateData("deleteDevice", { deviceId });
      return true;
    } catch (error) {
      return false;
    }
  };

  const addDevice = async (deviceData: TDevice) => {
    try {
      await mutate(
        (key: string) =>
          typeof key === "string" && key.includes("/api/attendance/devices"),
        (currentData: TDeviceResponse | undefined) => {
          if (!currentData) return currentData;

          return {
            ...currentData,
            data: [deviceData, ...currentData.data],
            meta: {
              ...currentData.meta,
              pagination: {
                ...currentData.meta.pagination,
                count: currentData.meta.pagination.count + 1,
              },
            },
          };
        },
        { revalidate: false },
      );

      return true;
    } catch (error) {
      console.error("addDevice mutation failed:", error);
      return false;
    }
  };

  const updateDevice = async (deviceId: string, deviceData: TDevice) => {
    try {
      await mutate(
        (key: string) =>
          typeof key === "string" &&
          key.includes("/api/attendance/devices") &&
          !key.includes(`/api/attendance/devices/${deviceId}`), // Exclude single device endpoints
        (currentData: TDeviceResponse | undefined) => {
          if (!currentData || !Array.isArray(currentData.data))
            return currentData;

          return {
            ...currentData,
            data: currentData.data.map((device) =>
              device.id === deviceId
                ? {
                    ...device,
                    attributes: deviceData.attributes,
                  }
                : device,
            ),
          };
        },
        { revalidate: false },
      );

      return true;
    } catch (error) {
      console.error("updateDevice mutation failed:", error);
      return false;
    }
  };

  return {
    devices: data?.data || [],
    pagination: data?.meta?.pagination,
    isLoading,
    error,
    deleteDevice,
    addDevice,
    updateDevice,
    isDeleting,
  };
};
