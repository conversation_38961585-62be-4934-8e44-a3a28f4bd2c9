"use client";

import { useApprovalMutations } from "./useApprovalMutations";
import { ApprovalRequestData } from "../type/approval-request";

/**
 * A backward compatibility hook for leave approvals
 * This uses the generic useApprovalMutations hook with type="leave"
 */
export const useLeaveApprovalMutations = () => {
  const {
    approveRequest,
    rejectRequest,
    withdrawLeaveRequest,
    isApproving,
    isRejecting,
    isWithdrawing,
  } = useApprovalMutations("leave");

  // Rename functions for backward compatibility
  const approveLeaveRequest = (
    approvalRequestId: string,
    comment: string = "",
    onSuccess?: (approvalRequest?: ApprovalRequestData) => void,
    onOptimisticUpdate?: () => void,
  ) => {
    return approveRequest(
      approvalRequestId,
      comment,
      onSuccess,
      onOptimisticUpdate,
    );
  };

  const rejectLeaveRequest = (
    approvalRequestId: string,
    comment: string = "",
    onSuccess?: (approvalRequest?: ApprovalRequestData) => void,
    onOptimisticUpdate?: () => void,
  ) => {
    return rejectRequest(
      approvalRequestId,
      comment,
      onSuccess,
      onOptimisticUpdate,
    );
  };

  return {
    approveLeaveRequest,
    rejectLeaveRequest,
    withdrawLeaveRequest,
    isApproving,
    isRejecting,
    isWithdrawing,
  };
};
