import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { AttendanceEventResponse } from "../type/employee-leaves";

export const useAttendanceListMutation = (
  page: number,
  limit: number,
  clientSearchParams: string = "",
) => {
  const { data, error, isLoading, mutate } = useSWR<AttendanceEventResponse>(
    `/api/attendance/events?include=employee&page[number]=${page}&page[size]=${limit}&${clientSearchParams.toString()}`,
    fetcher,
  );

  return {
    attendanceList: data?.data,
    meta: data?.meta,
    employeeData: data?.included,
    isLoading,
    error,
    mutateData: mutate,
  };
};
