import { TEmployeeAttachmentsResponse } from "../../type/employee";
import { fetcher } from "@/services/fetcher";
import { useState } from "react";
import { useToastMessage } from "@/hooks/use-toast-message";
import {
  renameAttachment as renameAttachmentAction,
  deleteAttachment as deleteAttachmentAction,
} from "@/app/[locale]/_modules/people/actions/attachments";
import { useOptimisticMutation } from "@/hooks/ues-optimistic-mutatation";

export const useEmployeeAttachments = (employeeId: string) => {
  const [isRenaming, setIsRenaming] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { showToast } = useToastMessage();

  const apiKey = employeeId ? `/api/employees/${employeeId}/attachments` : "";

  const { data, error, isLoading, mutateData } = useOptimisticMutation<
    TEmployeeAttachmentsResponse,
    { attachmentId: string; newName?: string }
  >({
    key: apiKey,
    fetcher,
    mutations: {
      renameAttachment: {
        updateFn: (currentData, { attachmentId, newName }) => {
          if (!currentData || !newName) return currentData;

          return {
            ...currentData,
            data: currentData.data.map((attachment) =>
              attachment.id === attachmentId
                ? {
                    ...attachment,
                    attributes: {
                      ...attachment.attributes,
                      filename: newName,
                    },
                  }
                : attachment,
            ),
          };
        },
        mutationFn: async ({ attachmentId, newName }) => {
          if (!newName) throw new Error("New name is required");

          try {
            setIsRenaming(true);
            const res: { error?: string } = await renameAttachmentAction(
              employeeId,
              attachmentId,
              newName,
            );
            if (res?.error)
              throw new Error(res.error || "Failed to rename attachment");
            showToast("success", "Attachment renamed successfully");
            return { data: undefined };
          } catch (error) {
            console.error("Error renaming attachment:", error);
            showToast(
              "error",
              error instanceof Error
                ? error.message
                : "Failed to rename attachment",
            );
            throw error;
          } finally {
            setIsRenaming(false);
          }
        },
      },

      deleteAttachment: {
        updateFn: (currentData, { attachmentId }) => {
          if (!currentData) return currentData;

          const updatedAttachments = currentData.data.filter(
            (attachment) => attachment.id !== attachmentId,
          );

          return {
            ...currentData,
            data: updatedAttachments,
            meta: {
              ...currentData.meta,
              pagination: {
                ...currentData.meta.pagination,
                count: updatedAttachments.length,
              },
            },
          };
        },
        mutationFn: async ({ attachmentId }) => {
          try {
            setIsDeleting(true);
            await deleteAttachmentAction(employeeId, attachmentId);
            showToast("success", "Attachment deleted successfully");
            return { data: undefined };
          } catch (error) {
            console.error("Error deleting attachment:", error);
            showToast(
              "error",
              error instanceof Error
                ? error.message
                : "Failed to delete attachment",
            );
            throw error;
          } finally {
            setIsDeleting(false);
          }
        },
      },
    },
    defaultData: {
      data: [],
      meta: {
        pagination: {
          count: 0,
          page: 1,
          limit: 20,
          from: 0,
          to: 0,
        },
      },
    },
    swrOptions: {
      keepPreviousData: true,
      dedupingInterval: 5000,
      revalidateAfterMutation: true,
    },
  });

  const renameAttachment = async (attachmentId: string, newName: string) => {
    try {
      await mutateData("renameAttachment", { attachmentId, newName });
      return true;
    } catch (error) {
      return false;
    }
  };

  const deleteAttachment = async (attachmentId: string) => {
    try {
      await mutateData("deleteAttachment", { attachmentId });
      return true;
    } catch (error) {
      return false;
    }
  };

  return {
    attachments: data?.data || [],
    totalCount: data?.meta?.pagination?.count || 0,
    isLoading,
    error,
    mutate: mutateData,
    isRenaming,
    isDeleting,
    renameAttachment,
    deleteAttachment,
  };
};
