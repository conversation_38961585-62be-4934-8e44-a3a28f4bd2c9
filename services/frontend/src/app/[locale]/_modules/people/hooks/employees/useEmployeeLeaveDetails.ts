import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { EmployeeLeavesResponse } from "../../type/employee-leaves";

export const useEmployeeLeaveDetails = (
  employeeId: string,
  page: number,
  limit: number,
) => {
  const apiKey = `/api/employees/${employeeId}/leaves?page=${page}&limit=${limit}`;
  const { data, error, isLoading, mutate } = useSWR<EmployeeLeavesResponse>(
    employeeId ? apiKey : null,
    fetcher,
    {
      dedupingInterval: 2000,
      keepPreviousData: true,
    },
  );

  return {
    leaveDetails: data?.data || [],
    pagination: data?.meta?.pagination,
    isLoading,
    error,
    mutate,
  };
};
