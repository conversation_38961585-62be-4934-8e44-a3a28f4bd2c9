import useS<PERSON> from "swr";
import {
  TEmployeeAttributes,
  TEmployeeData,
  TEmployeeResponse,
  TUserRoleIncluded,
} from "../../type/employee";
import { TSalaryPackageData } from "../../type/salary-package";
import { fetcher } from "@/services/fetcher";
import { KeyedMutator } from "swr";

export const useEmployeeDetails = (id: string) => {
  const {
    data,
    error,
    isLoading: swrIsLoading,
    mutate,
  } = useSWR<TEmployeeResponse>(
    id
      ? `/api/employees/${id}?include=user_roles.role,user_roles.project,salary_package`
      : null,
    fetcher,
  );
  const isLoading = !id || swrIsLoading;
  const employee: TEmployeeAttributes | null = data?.data?.attributes || null;
  const employeeData: TEmployeeData | null = data?.data || null;

  // Get all included data
  const included = data?.included || [];

  // pass all included data (user_roles, roles, and projects) to the component
  const userRoles: TUserRoleIncluded[] = included as TUserRoleIncluded[];

  const historySalaryPackage: TSalaryPackageData[] = [];
  // Find the salary package in the included data if it exists
  const salaryPackage: TSalaryPackageData | null =
    included.find(
      (item): item is TSalaryPackageData => item.type === "salary_package",
    ) || null;

  return {
    employee,
    employeeData,
    salaryPackage,
    historySalaryPackage,
    userRoles,
    isLoading,
    error,
    mutate: mutate as KeyedMutator<TEmployeeResponse>,
  };
};
