import { TEmployee } from "../type/employee";
import { TIncludedEmployee } from "../type/employee-leaves";

export function findEmployeeById(
  employeeData: TIncludedEmployee[],
  employeeId: string | undefined,
): TEmployee | null {
  if (!employeeId) return null;

  const foundEmployee = employeeData.find(
    (employee) => employee.id === employeeId,
  )?.attributes;

  return foundEmployee || null;
}
