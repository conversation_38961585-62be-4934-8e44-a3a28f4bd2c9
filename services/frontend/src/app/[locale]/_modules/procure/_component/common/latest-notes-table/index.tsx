"use client";

import { PaginationWithLinks } from "@/components/pagination-with-links";
import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { columns } from "./latest-notes-columns";
import { RowSelectionState } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { sampleNotes } from "@/app/[locale]/procure/tempDate";

type LatestNoteTableProps = {
  showPagination: boolean;
  searchParams: {
    page: string;
    limit: string;
  };
};

const LatestNoteTable = ({
  showPagination,
  searchParams,
}: LatestNoteTableProps) => {
  const t = useTranslations();
  const locale = useLocale();
  // Sample data for demonstration
  const totalCount = sampleNotes.length;
  const isLoading = false;
  const error = undefined;

  const limit = parseInt(searchParams.limit ?? 5, 10);
  const page = parseInt(searchParams.page ?? 1, 10);

  const pagination = {
    page: page,
    firstResult: (page - 1) * limit + 1,
    lastResult: Math.min(page * limit, totalCount),
  };

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  return (
    <>
      <DataTable
        data={sampleNotes}
        dataCount={totalCount}
        columns={columns}
        tableContainerClass="min-h-[240px] text-start"
        meta={{
          t,
          locale: locale,
        }}
        rowSelection={rowSelection}
        hideSearch={true}
        hideFilters={true}
        hideColumns={true}
        hideTableHeader={true}
        onRowSelectionChange={setRowSelection}
        translationPrefix="procure.HomePage.table"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
        tableHeadStyle="text-start"
        tableCellStyle="text-start"
        onRowClick={(row) => {
          alert("row Clicked");
        }}
      />
      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={pagination.page}
            pageSize={limit}
            totalCount={totalCount}
            firstLastCounts={{
              firstCount: pagination.firstResult,
              lastCount: pagination.lastResult,
            }}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
            isLoading={isLoading}
            isDisabled={totalCount === 0}
          />
        </div>
      )}
    </>
  );
};

export default LatestNoteTable;
