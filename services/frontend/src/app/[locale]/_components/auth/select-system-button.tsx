// src/components/shared/system-selector.tsx
"use client";

import { startTransition, useActionState, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { getSystemsArr } from "@/constants";
import { cn, filterSystems } from "@/lib/utils";
import { ActionState, TFunction, TSystem, TSystems } from "@/types";
import Image from "next/image";
import { useUser } from "@/contexts/user-provider";
import { getSession } from "@/server/actions/auth";
import { Skeleton } from "@/components/ui/skeleton";
import { getCookie } from "cookies-next/client";
import { saveDataToSessionStorage } from "@/lib/local-storage";

type SystemSelectorProps = {
  locale: string;
  selectButtonStyle?: string;
  systemTitleStyle?: string;
  t: TFunction;
  forceLoading?: boolean;
};

const SystemSelector = ({
  locale,
  t,
  selectButtonStyle,
  systemTitleStyle,
  forceLoading = false,
}: SystemSelectorProps) => {
  const router = useRouter();
  const { user, isLoading } = useUser();
  const currSystem = getCookie("currSystem") as TSystems;

  // Initialize action state for session creation
  const initialState: ActionState<null> = {
    error: "",
    success: "",
    issues: [],
    data: null,
  };

  // Memoize systems to prevent unnecessary recalculations
  const systems = getSystemsArr(t);
  const filteredSystems = filterSystems(
    systems,
    user?.systems ? user.systems : [],
  );

  const [state, formAction, isPending] = useActionState(
    getSession,
    initialState,
  );
  const [selectedSystem, setSelectedSystem] = useState<string | null>(null);
  const [processingSystem, setProcessingSystem] = useState<string | null>(null);

  const handleSelectSystem = async (system: string) => {
    try {
      setSelectedSystem(system);
      setProcessingSystem(system);

      const formData = new FormData();
      formData.append("system", system.replace("/", ""));

      startTransition(() => formAction(formData));
    } catch (error) {
      setProcessingSystem(null);
    }
  };

  useEffect(() => {
    if (state.success && selectedSystem) {
      // Cache permissions if they were fetched by the server action
      if (state.data) {
        const systemName = selectedSystem.replace("/", "") as TSystems;
        const storageKey = `user_permissions_${systemName}`;
        saveDataToSessionStorage(storageKey, state.data);
      }

      // Navigate to the selected system
      router.push(`/${locale}/${selectedSystem}`);
    }
  }, [state.success, state.data, selectedSystem, locale, router]);

  const renderSystemButton = (system: TSystem) => {
    const isProcessing = processingSystem === system.href;
    const isDisabled = isPending || state.success !== "";
    const isCurrSystem = currSystem === system.name;

    return (
      <Button
        key={system.title}
        variant="outline"
        className={cn(
          `flex justify-start items-center w-full h-20 gap-2 p-4 hover:border-2 hover:border-secondary hover:bg-[#0CA58B1A]`,

          selectButtonStyle,
          {
            "border border-solid": isDisabled || isCurrSystem,
          },
        )}
        onClick={() => handleSelectSystem(system.href)}
        disabled={isDisabled || isCurrSystem}
      >
        {isProcessing && isDisabled ? (
          <span className="animate-spin flex justify-center items-center w-[50px] h-[60px]">
            ⏳
          </span>
        ) : (
          <div className="w-[50px] min-w-[50px] h-[60px] relative">
            <Image
              className="w-full h-full"
              src={system.img}
              fill={true}
              alt={system.alt}
              priority={true}
            />
          </div>
        )}
        <div className="flex flex-col space-y-1 text-start items-start">
          <span
            className={`font-bold [font-size:_clamp(12px,4vw,16px)] leading-[135%] text-lg whitespace-break-spaces text-[#375333] ${systemTitleStyle}`}
          >
            {system.display_Name}
          </span>
          <span className="font-normal [font-size:_clamp(10px,4vw,12px)] leading-[150%] text-green-800">
            {system.description}
          </span>
        </div>
      </Button>
    );
  };

  const showLoading =
    forceLoading || isLoading || (user === null && isLoading !== false);

  return (
    <div className="min-h-[270px] w-full flex flex-col items-center justify-center">
      {showLoading ? (
        <div className="text-center text-gray-500 flex flex-col w-full justify-start items-center gap-3">
          {[1, 2, 3].map((idx) => (
            <Skeleton key={idx} className="w-full h-20 p-4 bg-gray-200" />
          ))}
        </div>
      ) : filteredSystems.length > 0 ? (
        <ul className="space-y-3 w-full">
          {filteredSystems.map((system) => (
            <li key={system.href}>{renderSystemButton(system)}</li>
          ))}
        </ul>
      ) : (
        <p className="text-center text-gray-500">
          {user?.email}
          {t("auth.selectSystem.noSystems")}
        </p>
      )}

      {state.error && (
        <p role="alert" className="text-destructive text-sm mt-4 text-center">
          {state.error}
        </p>
      )}
    </div>
  );
};

export default SystemSelector;
