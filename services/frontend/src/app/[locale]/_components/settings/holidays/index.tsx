"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import HolidayCalendar from "./holiday-calendar";
import HolidayForm from "./holiday-form";
import HolidayDetailsModal from "./holiday-details-modal";
import dynamic from "next/dynamic";
import Loader from "@/components/loader";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { useAttendanceExemptions } from "@/app/[locale]/_modules/people/hooks/attendance/useAttendanceExemptions";
import { THoliday } from "@/types/settings/holidays";

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
  },
);

const Holidays = () => {
  const t = useTranslations() as TFunction;
  const { holidays } = useAttendanceExemptions();
  const [showAddForm, setShowAddForm] = useState(false);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [selectedHoliday, setSelectedHoliday] = useState<THoliday | null>(null);

  const handleAddHoliday = () => {
    setShowAddForm(true);
  };

  const handleHolidayClick = (holiday: THoliday) => {
    setSelectedHoliday(holiday);
    setIsDetailsOpen(true);
  };

  const handleFormSuccess = () => {
    setShowAddForm(false);
  };

  const handleFormCancel = () => {
    setShowAddForm(false);
  };

  return (
    <div className={`space-y-6`}>
      <HolidayCalendar
        holidays={holidays ?? []}
        onAddHoliday={handleAddHoliday}
        onHolidayClick={handleHolidayClick}
      />

      {/* Add Holiday Form Dialog */}
      <ResponsiveDialog
        open={showAddForm}
        onOpenChange={setShowAddForm}
        header={
          <div className="p-6 border border-b">
            <DialogTitle className="text-lg font-semibold">
              {t("settings.holidays.form.add-title")}
            </DialogTitle>
            <DialogDescription className="text-sm text-muted-foreground sr-only">
              {t("settings.holidays.form.add-description")}
            </DialogDescription>
          </div>
        }
      >
        <HolidayForm
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      </ResponsiveDialog>

      {/* Holiday Details Modal */}
      <HolidayDetailsModal
        holiday={selectedHoliday}
        isOpen={isDetailsOpen}
        onClose={() => setIsDetailsOpen(false)}
      />
    </div>
  );
};

export default Holidays;
