import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const response = await peopleService.addNewEmployee(formData);
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error adding new employee:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      { error: "Failed to add employee", message: errorMessage },
      { status: 500 },
    );
  }
}
