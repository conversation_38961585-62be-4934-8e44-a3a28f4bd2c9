import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    try {
      const response = await peopleService.getPeopleAttendance(
        decodeURIComponent(searchParams.toString()),
      );

      // Return the response directly without wrapping it
      return NextResponse.json(response);
    } catch (error) {
      return NextResponse.json(
        { error: "Failed to fetch Attendance list" },
        { status: 500 },
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
