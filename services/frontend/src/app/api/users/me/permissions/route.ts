import { NextRequest, NextResponse } from "next/server";
import { CoreAPI } from "@/services/api/core";
import { TSystems } from "@/types";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const scope = (searchParams.get("scope") as TSystems) || "core";

    const coreApi = new CoreAPI();
    const response = await coreApi.getPermissions({ scope });
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching system user data:", error);
    return NextResponse.json(
      { error: "Failed to fetch system user data" },
      { status: 500 },
    );
  }
}
