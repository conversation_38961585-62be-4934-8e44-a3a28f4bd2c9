import { cookies } from "next/headers";

export class BaseAPI {
  constructor(protected baseURL: string) {}

  /**
   * Makes a request to the API with JSON data
   */
  protected async request<T>(path: string, options: RequestInit): Promise<T> {
    const cookieStore = await cookies();
    const lang = cookieStore.get("NEXT_LOCALE")?.value || "ar";

    const headers = {
      "Accept-Language": lang,
      "Content-Type": "application/json",
      ...(options.headers || {}),
    };

    return this.processRequest<T>(path, {
      ...options,
      headers,
    });
  }

  /**
   * Makes a request to the API with FormData
   */
  protected async requestWithFormData<T>(
    path: string,
    options: RequestInit & { body: FormData },
  ): Promise<T> {
    const cookieStore = await cookies();
    const lang = cookieStore.get("NEXT_LOCALE")?.value || "ar";

    const headers = {
      "Accept-Language": lang,
      // Don't set Content-Type for FormData
      ...(options.headers || {}),
    };

    return this.processRequest<T>(path, {
      ...options,
      headers,
    });
  }

  /**
   * Processes the request and handles common response logic
   */
  private async processRequest<T>(
    path: string,
    options: RequestInit,
  ): Promise<T> {
    const requestId = Math.random().toString(36).substr(2, 9);
    const startTime = Date.now();

    const url = `${this.baseURL.replace(/\/$/, "")}/${path.replace(/^\//, "")}`;
    console.log(`🌐 [${requestId}] ${options.method || "GET"} ${url}`);

    // Log the API request as a curl command
    const curlParts = [
      "curl",
      "-X",
      options.method || "GET",
      `'${this.baseURL}/${path}'`,
    ];

    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        curlParts.push("-H", `'${key}: ${value}'`);
      });
    }

    if (options.body) {
      if (typeof options.body === "string") {
        curlParts.push("--data", `'${options.body}'`);
      } else if (options.body instanceof FormData) {
        const entries = Array.from(options.body.entries());
        console.log(`🌐 [${requestId}] FormData: ${entries.length} fields`);

        for (const [key, value] of entries) {
          curlParts.push("--form", `'${key}="${value}"'`);
        }

        if (entries.length === 0) {
          console.log(`⚠️ [${requestId}] WARNING: FormData is empty!`);
        }
      }
    }

    console.debug(`🌐 [${requestId}] curl:`, curlParts.join(" "));

    try {

      const fetchOptions = {
        ...options,
        redirect: "follow" as RequestRedirect,
        mode: "cors" as RequestMode,
      };

      const res = await fetch(url, fetchOptions);

      const fetchDuration = Date.now() - startTime;
      console.log(`🌐 [${requestId}] Response: ${res.status} ${res.statusText} (${fetchDuration}ms)`);

      if (res.redirected) {
        console.log(`🔄 [${requestId}] Redirected: ${url} → ${res.url}`);
      }

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        console.log(`❌ [${requestId}] Error ${res.status}:`, errorData);

        (errorData as any).status = res.status;
        (errorData as any).data = errorData;
        const error = new Error(
          errorData.error || errorData.errors?.[0]?.detail || res.statusText,
        );
        throw error;
      }

      const data = await res.json();
      const totalDuration = Date.now() - startTime;

      if (!data) {
        console.log(`❌ [${requestId}] No data returned from API`);
        const error = new Error("No data returned from API");
        (error as any).status = res.status;
        throw error;
      }

      console.log(`✅ [${requestId}] Success (${totalDuration}ms)`);
      return data as T;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`❌ [${requestId}] Request failed (${duration}ms):`, error instanceof Error ? error.message : error);
      throw error;
    }
  }
}

export const getCoreSessionToken = async (): Promise<string | null> => {
  const cookieStore = await cookies();

  return cookieStore.get("core_session_token")?.value || null;
};

export const getMainToken = async (): Promise<string | null> => {
  const cookieStore = await cookies();
  return cookieStore.get("main_token")?.value || null;
};

export const getUserLanguage = async (): Promise<string> => {
  const cookieStore = await cookies();
  return cookieStore.get("NEXT_LOCALE")?.value || "ar";
};

export const getCurrSystem = async (): Promise<string> => {
  const cookieStore = await cookies();
  return cookieStore.get("currSystem")?.value || "core";
};
