import { MetricCardTypes } from "@/enums/statistics";

export type Tinput<PERSON>ield<T extends Record<string, unknown>> = {
  name?: keyof T;
  label?: string;
  placeholder?: string;
  className?: string;
  labelClassName?: string;
  leftIcon?: string;
  rightIcon?: string;
  disableToggle?: boolean;
  formatOnBlur?: boolean;
  formatOnChange?: boolean;
  readOnly?: boolean;
  onClick?: () => void;
  options?: Array<{
    value: string;
    label: string;
  }>;
  // Profile image input specific props
  defaultImage?: string;
  fallbackText?: string;
  fallbackInitials?: string;
  avatarSize?: "sm" | "md" | "lg" | "xl";
  containerClassName?: string;
  buttonsClassName?: string;
  deleteButtonText?: string;
  addBtnClassName?: string;
  deleteBtnClassName?: string;
  changeButtonText?: string;
  onImageChange?: (file: File | null | string) => void;
  deleteTranslationKey?: string;
  changeTranslationKey?: string;
  changeButtonIcon?: React.ReactNode;

  // File attachments input specific props
  accept?: string;
  multiple?: boolean;
  maxFiles?: number;
  maxSize?: number;
  inputClassName?: string;
  fileListClassName?: string;
  fileClassName?: string;
  buttonClassName?: string;
  uploadButtonText?: string;
  uploadTranslationKey?: string;
  onFilesChange?: (files: File[]) => void;
  uploadButtonIcon?: React.ReactNode;
  enableDragDrop?: boolean;

  // DateRange field specific props
  title?: string;
  description?: string;
  submitButtonText?: string;
  startDateLabel?: string;
  endDateLabel?: string;
  allowPastDates?: boolean;
  minDate?: Date;
  maxDate?: Date;
  type?:
    | "button"
    | "checkbox"
    | "color"
    | "date"
    | "datetime-local"
    | "email"
    | "file"
    | "hidden"
    | "image"
    | "month"
    | "number"
    | "password"
    | "radio"
    | "range"
    | "reset"
    | "search"
    | "submit"
    | "tel"
    | "text"
    | "time"
    | "url"
    | "week"
    | "textarea"
    | "select"
    // Custom field types
    | "attachments"
    | "assignments"
    | "multiDateSelector"
    | "dateRange"
    | "ip-address"
    | "port";
};

export type TSystems = "core" | "people" | "procure" | "cm";

// types.ts (or a similar file)
export type TSystem = {
  href: string;
  name: string;
  display_Name: string;
  title: string;
  description: string;
  img: string;
  alt: string;
};

export type SidebarItem = {
  title: string;
  url: string;
  icon?: string;
  permission?: string[];
};

export type SidebarData = {
  head: string;
  items?: SidebarItem[];
  links?: string[];
  icon?: string;
};

// settings types
export type TChangePasswordFormProps = {
  onPasswordChanged?: () => void;
};

// navbar notifications
export type Notification = {
  id: number;
  title: string;
  description: string;
  date: string;
  isRead: boolean;
};

// Define a generic translation function type
export type TFunction = (key: string, values?: Record<string, any>) => string;

// search params type
export type searchParams = { [key: string]: string | string[] | undefined };

//global initial state for useActionState
export type ActionState<T> = {
  error?: string | null;
  success?: string | null;
  issues?: string[] | null;
  data?: T | null;
  redirectTo?: string | null;
  redirect?: string | null; // Alias for redirectTo for backward compatibility
  status?: number;
};

// global chart header
export type ChartHeader = {
  headerClassName?: string;
  title?: string;
  titleStyle?: string;
  selectPlaceholder?: string;
  selectOptions?: { value: string; label: string; disabled?: boolean }[];
  selectValue?: string;
  onSelectChange?: (value: string) => void;
  legendLabels?: string[];
  selectTriggerStyle?: string;
};

// Standardized API response format for the entire application
export type ApiResponse<T, M = Record<string, unknown>> = {
  data: {
    id: string;
    type: string;
    attributes: T;
  };
  meta: M;
};

export type MetricCard = {
  id: MetricCardTypes;
  type: "metric_card";
  attributes: {
    title: string;
    value: string;
    unit: string;
    comparison: {
      percentage: number;
      trend: "up" | "down" | "neutral";
      text: string;
    };
  };
};

export type MetricCardResponse = {
  data: MetricCard[];
};
