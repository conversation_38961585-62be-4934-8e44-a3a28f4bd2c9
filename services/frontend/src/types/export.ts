import { ExportFormat } from "@/config/export-entities";

// Re-export ExportFormat for convenience
export type { ExportFormat };

export type ExportFilters = {
  search?: string;
  sort?: string;
  page?: number;
  limit?: number;
  include?: string;
  columns?: string;
  // Allow additional filter parameters
  [key: string]: string | number | boolean | undefined;
};

export type ExportOptions = {
  onSuccess?: (entity: string, format: ExportFormat) => void;
  onError?: (error: string, entity: string, format: ExportFormat) => void;
};

export type ExportConfig = {
  entity: string;
  enabled?: boolean;
  enhanced?: boolean; // Enable enhanced export with column selection
  customFilters?: Record<string, string | number | boolean>;
  columnCategories?: Record<string, string>;
  columnDescriptions?: Record<string, string>;
  requiredColumns?: string[];
  excludeColumns?: string[];
  defaultSelectedColumns?: string[];
};

export type ExportState = {
  loading: boolean;
  error: string | null;
  lastExport?: {
    entity: string;
    format: ExportFormat;
    timestamp: Date;
  };
};

export type ExportResponse = {
  success: boolean;
  error?: string;
  filename?: string;
};

// API Request/Response types
export type ExportApiRequest = {
  entity: string;
  format: ExportFormat;
  filters?: ExportFilters;
};

export type ExportApiError = {
  error: string;
  status?: number;
  details?: string;
};

// Hook return type
export type UseGenericExportReturn = {
  exportData: (format: ExportFormat, filters?: ExportFilters) => Promise<void>;
  loading: boolean;
  error: string | null;
  clearError: () => void;
  isSupported: (format: ExportFormat) => boolean;
  supportedFormats: ExportFormat[];
  availableSubsystems: string[];
  currentSubsystem: string | undefined;
  switchSubsystem: (subsystem: string) => void;
};

// DataTable export configuration
export interface DataTableExportConfig extends ExportConfig {
  maxRecords?: number;
}
