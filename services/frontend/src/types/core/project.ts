export type TProjectAttributes = {
  name: string;
  description?: string;
  status?: string;
};

export type TProjectRelationship = {
  id: string;
  type: string;
};

export type TProjectRelationships = {
  roles: {
    data: TProjectRelationship[];
  };
  users: {
    data: TProjectRelationship[];
  };
};

export type TProject = {
  id: string;
  type: string;
  attributes: TProjectAttributes;
  relationships: TProjectRelationships;
};

export type TProjectsResponse = {
  data: TProject[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};
